import { ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchIconsStart,
  fetchIconsSuccess,
  fetchIconsFailure
} from '../redux/bankIconSlice';

// Epic for fetching all icons
export const bankIconEpic = (action$) =>
  action$.pipe(
    ofType(fetchIconsStart.type),
    mergeMap(() =>
      from(axiosInstance.get('/pennypal/api/icons/list')).pipe(
        map((response) => fetchIconsSuccess(response.data)),
        catchError((error) => of(fetchIconsFailure(error.message)))
      )
    )
  );
export default bankIconEpic;