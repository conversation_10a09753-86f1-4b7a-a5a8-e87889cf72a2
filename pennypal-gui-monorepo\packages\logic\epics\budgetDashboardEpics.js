import { ofType } from 'redux-observable';
import { of, from } from 'rxjs';
import { mergeMap, map, catchError, withLatestFrom } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {getCurrentUserId } from '../../web/src/utils/AuthUtil';
import {
  fetchBudgetDashboardData,
  fetchBudgetDashboardDataSuccess,
  fetchBudgetDashboardDataFailure
} from '../redux/budgetDashboardSlice';


// Updated action creator - only needs year and month
export const fetchBudgetDashboard = (year, month) => ({
  type: fetchBudgetDashboardData.type,
  payload: { year, month }
});

export const getBudgetDashboardEpic = (action$, state$) => {
  return action$.pipe(
    ofType(fetchBudgetDashboardData.type),
    withLatestFrom(state$),
    mergeMap(([action, state]) => {
      const userId = getCurrentUserId(); // Get userId from token
      const { year, month } = action.payload;
      
      // Check if user is authenticated
      if (!userId) {
        return of(fetchBudgetDashboardDataFailure({
          error: 'User not authenticated'
        }));
      }
      
      return from(
        axiosInstance.get(`/pennypal/api/v1/budget/summary_by_month/${userId}/${year}/${month}`)
      ).pipe(
        map(response => {
          console.log('Budget summary data fetched:', response.data);
          return fetchBudgetDashboardDataSuccess({ data: response.data });
        }),
        catchError(error => {
          console.error('Error fetching budget summary data:', error);
          return of(fetchBudgetDashboardDataFailure({
            error: error.response?.data || 'Failed to fetch budget summary data'
          }));
        })
      );
    })
  );
};

export default [getBudgetDashboardEpic];