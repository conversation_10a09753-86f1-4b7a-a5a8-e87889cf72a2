import { ofType, combineEpics } from 'redux-observable';
import { from, of, merge } from 'rxjs';
import { catchError, map, mergeMap, filter, switchMap, delay } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchIconsStart,
  fetchIconsSuccess,
  fetchIconsFailure,
  fetchSubCategoryIconsStart,
  fetchSubCategoryIconsSuccess,
  fetchSubCategoryIconsFailure,
  fetchCategoriesStart,
  fetchCategoriesSuccess,
  fetchCategoriesFailure,
  fetchSubcategoriesStart,
  fetchSubcategoriesSuccess,
  fetchSubcategoriesFailure,
  fetchTransactionsStart,
  fetchTransactionsSuccess,
  fetchTransactionsFailure,
  fetchRecurringTransactionsStart,
  fetchRecurringTransactionsSuccess,
  fetchRecurringTransactionsFailure,
  fetchFutureRecurringTransactionsStart,
  fetchFutureRecurringTransactionsSuccess,
  fetchFutureRecurringTransactionsFailure,
  fetchBudgetSummaryStart,
  fetchBudgetSummarySuccess,
  fetchBudgetSummaryFailure,
  fetchBudgetDataStart,
  fetchBudgetDataSuccess,
  fetchBudgetDataFailure
} from '../redux/cacheSlice';
import {
  signInSuccess,
  googleSignInSuccess,
  otpVerifySuccess,
  registrationSuccess
} from '../redux/authSlice';

// Epic to initialize cache on login success
export const initializeCacheOnLoginEpic = (action$, state$) =>
  action$.pipe(
    ofType(
      signInSuccess.type,
      googleSignInSuccess.type,
      otpVerifySuccess.type,
      registrationSuccess.type
    ),
    filter((action) => {
      // Only initialize if user is authenticated and has tokens
      return action.payload?.jwtToken || action.payload?.user?.jwtToken;
    }),
    switchMap(() => {
      try {
        const state = state$.value;
        const cache = state?.cache;

        // Safety check - ensure cache state exists
        if (!cache) {
          console.log('⚠️ Cache state not initialized yet, skipping cache initialization');
          return of({ type: 'cache/initializeSkipped' });
        }

        // Check if we already have all data cached
        const allDataCached = cache.iconsLoaded &&
                             cache.subCategoryIconsLoaded &&
                             cache.categoriesLoaded &&
                             cache.subcategoriesLoaded;

        if (allDataCached) {
          console.log('✅ All cache data already loaded, skipping initialization');
          return of({ type: 'cache/initializeComplete' });
        }

        console.log('🔄 Initializing cache data after login...');

        // Get user ID from auth state
        const userId = state?.auth?.user?.id;
        if (!userId) {
          console.warn('⚠️ User ID not found, skipping cache initialization');
          return of({ type: 'cache/initializeSkipped' });
        }

        // Get current date for budget summary
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth() + 1; // JavaScript months are 0-based

        // Dispatch individual start actions for each API
        return merge(
          of(fetchIconsStart()).pipe(delay(500)),
          of(fetchSubCategoryIconsStart()).pipe(delay(500)),
          of(fetchCategoriesStart()).pipe(delay(500)),
          of(fetchSubcategoriesStart()).pipe(delay(500)),
          of(fetchTransactionsStart()).pipe(delay(500)),
          of(fetchRecurringTransactionsStart()).pipe(delay(500)),
          of(fetchFutureRecurringTransactionsStart()).pipe(delay(500)),
          of(fetchBudgetSummaryStart({ userId, year: currentYear, month: currentMonth })).pipe(delay(500)),
          of(fetchBudgetDataStart({ userId })).pipe(delay(500))
        );
      } catch (error) {
        console.error('❌ Error in cache initialization:', error);
        return of({ type: 'cache/initializeError', payload: error.message });
      }
    })
  );

// Epic for fetching icons
export const fetchIconsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchIconsStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.iconsLoaded;
        console.log('🔍 Icons epic filter check:', {
          cacheExists: !!cache,
          iconsLoaded: cache?.iconsLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for icons:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting icons API call...');
      return from(axiosInstance.get('/pennypal/api/icons/list')).pipe(
        map((response) => {
          console.log('✅ Icons fetched successfully:', response.data?.length || 0, 'items');
          return fetchIconsSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch icons:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch icons';
          return of(fetchIconsFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching subcategory icons
export const fetchSubCategoryIconsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchSubCategoryIconsStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        // Only check if data is already loaded, don't check loading state
        const shouldFetch = cache && !cache.subCategoryIconsLoaded;
        console.log('🔍 SubCategory icons epic filter check:', {
          cacheExists: !!cache,
          subCategoryIconsLoaded: cache?.subCategoryIconsLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for subcategory icons:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting subcategory icons API call...');
      return from(axiosInstance.get('/pennypal/api/v1/subCategory/icons')).pipe(
        map((response) => {
          console.log('✅ Subcategory icons fetched successfully:', response.data?.length || 0, 'items');
          // Process the icons to ensure they have the correct format
          const processedIcons = (response.data || []).filter(dto =>
            dto.subCategoryId && dto.base64Icon
          );
          return fetchSubCategoryIconsSuccess(processedIcons);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch subcategory icons:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch subcategory icons';
          return of(fetchSubCategoryIconsFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching categories
export const fetchCategoriesEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchCategoriesStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        // Only check if data is already loaded, don't check loading state
        const shouldFetch = cache && !cache.categoriesLoaded;
        console.log('🔍 Categories epic filter check:', {
          cacheExists: !!cache,
          categoriesLoaded: cache?.categoriesLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for categories:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting categories API call...');
      return from(axiosInstance.get('/pennypal/api/v1/category/all')).pipe(
        map((response) => {
          console.log('✅ Categories fetched successfully:', response.data?.length || 0, 'items');
          return fetchCategoriesSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch categories:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch categories';
          return of(fetchCategoriesFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching subcategories
export const fetchSubcategoriesEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchSubcategoriesStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        // Only check if data is already loaded, don't check loading state
        const shouldFetch = cache && !cache.subcategoriesLoaded;
        console.log('🔍 Subcategories epic filter check:', {
          cacheExists: !!cache,
          subcategoriesLoaded: cache?.subcategoriesLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for subcategories:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting subcategories API call...');
      return from(axiosInstance.get('/pennypal/api/v1/subCategory/all')).pipe(
        map((response) => {
          console.log('✅ Subcategories fetched successfully:', response.data?.length || 0, 'items');
          return fetchSubcategoriesSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch subcategories:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch subcategories';
          return of(fetchSubcategoriesFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching transactions
export const fetchTransactionsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchTransactionsStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.transactionsLoaded;
        console.log('🔍 Transactions epic filter check:', {
          cacheExists: !!cache,
          transactionsLoaded: cache?.transactionsLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for transactions:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting transactions API call...');
      const userId = state$.value?.auth?.user?.id;
      if (!userId) {
        console.error('❌ User ID not found for transactions API');
        return of(fetchTransactionsFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/transaction/transactions/user/${userId}?page=0&pageSize=250`)).pipe(
        map((response) => {
          console.log('✅ Transactions fetched successfully:', response.data?.content?.length || 0, 'items');
          return fetchTransactionsSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch transactions:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch transactions';
          return of(fetchTransactionsFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching recurring transactions
export const fetchRecurringTransactionsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchRecurringTransactionsStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.recurringTransactionsLoaded;
        console.log('🔍 Recurring transactions epic filter check:', {
          cacheExists: !!cache,
          recurringTransactionsLoaded: cache?.recurringTransactionsLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for recurring transactions:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting recurring transactions API call...');
      const userId = state$.value?.auth?.user?.id;
      if (!userId) {
        console.error('❌ User ID not found for recurring transactions API');
        return of(fetchRecurringTransactionsFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/transaction/recurring/fetch2/${userId}`)).pipe(
        map((response) => {
          console.log('✅ Recurring transactions fetched successfully:', response.data?.length || 0, 'items');
          return fetchRecurringTransactionsSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch recurring transactions:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch recurring transactions';
          return of(fetchRecurringTransactionsFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching future recurring transactions
export const fetchFutureRecurringTransactionsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchFutureRecurringTransactionsStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.futureRecurringTransactionsLoaded;
        console.log('🔍 Future recurring transactions epic filter check:', {
          cacheExists: !!cache,
          futureRecurringTransactionsLoaded: cache?.futureRecurringTransactionsLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for future recurring transactions:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting future recurring transactions API call...');
      const userId = state$.value?.auth?.user?.id;
      if (!userId) {
        console.error('❌ User ID not found for future recurring transactions API');
        return of(fetchFutureRecurringTransactionsFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/transaction/recurring/fetch_future/${userId}`)).pipe(
        map((response) => {
          console.log('✅ Future recurring transactions fetched successfully:', response.data?.length || 0, 'items');
          return fetchFutureRecurringTransactionsSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch future recurring transactions:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch future recurring transactions';
          return of(fetchFutureRecurringTransactionsFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching budget summary
export const fetchBudgetSummaryEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchBudgetSummaryStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.budgetSummaryLoaded;
        console.log('🔍 Budget summary epic filter check:', {
          cacheExists: !!cache,
          budgetSummaryLoaded: cache?.budgetSummaryLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for budget summary:', error);
        return false;
      }
    }),
    mergeMap((action) => {
      console.log('🚀 Starting budget summary API call...');
      const { userId, year, month } = action.payload || {};

      if (!userId || !year || !month) {
        console.error('❌ Missing parameters for budget summary API:', { userId, year, month });
        return of(fetchBudgetSummaryFailure('Missing required parameters'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/budget/summary_by_month/${userId}/${year}/${month}`)).pipe(
        map((response) => {
          console.log('✅ Budget summary fetched successfully');
          return fetchBudgetSummarySuccess(response.data);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch budget summary:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch budget summary';
          return of(fetchBudgetSummaryFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching budget data
export const fetchBudgetDataEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchBudgetDataStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.budgetDataLoaded;
        console.log('🔍 Budget data epic filter check:', {
          cacheExists: !!cache,
          budgetDataLoaded: cache?.budgetDataLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for budget data:', error);
        return false;
      }
    }),
    mergeMap((action) => {
      console.log('🚀 Starting budget data API call...');
      const { userId } = action.payload || {};

      if (!userId) {
        console.error('❌ User ID not found for budget data API');
        return of(fetchBudgetDataFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/budget/user/${userId}/month`)).pipe(
        map((response) => {
          console.log('✅ Budget data fetched successfully:', response.data?.length || 0, 'items');
          return fetchBudgetDataSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch budget data:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch budget data';
          return of(fetchBudgetDataFailure(errorMessage));
        })
      );
    })
  );

// Combined cache epic
export const cacheEpic = combineEpics(
  initializeCacheOnLoginEpic,
  fetchIconsEpic,
  fetchSubCategoryIconsEpic,
  fetchCategoriesEpic,
  fetchSubcategoriesEpic,
  fetchTransactionsEpic,
  fetchRecurringTransactionsEpic,
  fetchFutureRecurringTransactionsEpic,
  fetchBudgetSummaryEpic,
  fetchBudgetDataEpic
);

export default cacheEpic;