import { ofType } from 'redux-observable';
import { from, of, merge } from 'rxjs';
import { catchError, map, mergeMap, filter, switchMap } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchIconsStart,
  fetchIconsSuccess,
  fetchIconsFailure,
  fetchSubCategoryIconsStart,
  fetchSubCategoryIconsSuccess,
  fetchSubCategoryIconsFailure,
  fetchCategoriesStart,
  fetchCategoriesSuccess,
  fetchCategoriesFailure,
  fetchSubcategoriesStart,
  fetchSubcategoriesSuccess,
  fetchSubcategoriesFailure,
  initializeCacheStart
} from '../redux/cacheSlice';
import {
  signInSuccess,
  googleSignInSuccess,
  otpVerifySuccess,
  registrationSuccess
} from '../redux/authSlice';

// Epic to initialize cache on login success
export const initializeCacheOnLoginEpic = (action$, state$) =>
  action$.pipe(
    ofType(
      signInSuccess.type,
      googleSignInSuccess.type,
      otpVerifySuccess.type,
      registrationSuccess.type
    ),
    filter((action) => {
      // Only initialize if user is authenticated and has tokens
      return action.payload?.jwtToken || action.payload?.user?.jwtToken;
    }),
    switchMap(() => {
      const state = state$.value;
      const cache = state.cache;
      
      // Check if we already have all data cached
      const allDataCached = cache.iconsLoaded && 
                           cache.subCategoryIconsLoaded && 
                           cache.categoriesLoaded && 
                           cache.subcategoriesLoaded;
      
      if (allDataCached) {
        console.log('✅ All cache data already loaded, skipping initialization');
        return of({ type: 'cache/initializeComplete' });
      }
      
      console.log('🔄 Initializing cache data after login...');
      
      // Dispatch start action to set loading states
      return of(initializeCacheStart());
    })
  );

// Epic for fetching icons
export const fetchIconsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchIconsStart.type, initializeCacheStart.type),
    filter(() => {
      const cache = state$.value.cache;
      return !cache.iconsLoaded && !cache.iconsLoading;
    }),
    mergeMap(() =>
      from(axiosInstance.get('/pennypal/api/icons/list')).pipe(
        map((response) => {
          console.log('✅ Icons fetched successfully:', response.data?.length || 0, 'items');
          return fetchIconsSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch icons:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch icons';
          return of(fetchIconsFailure(errorMessage));
        })
      )
    )
  );

// Epic for fetching subcategory icons
export const fetchSubCategoryIconsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchSubCategoryIconsStart.type, initializeCacheStart.type),
    filter(() => {
      const cache = state$.value.cache;
      return !cache.subCategoryIconsLoaded && !cache.subCategoryIconsLoading;
    }),
    mergeMap(() =>
      from(axiosInstance.get('/pennypal/api/v1/subCategory/icons')).pipe(
        map((response) => {
          console.log('✅ Subcategory icons fetched successfully:', response.data?.length || 0, 'items');
          // Process the icons to ensure they have the correct format
          const processedIcons = (response.data || []).filter(dto =>
            dto.subCategoryId && dto.base64Icon
          );
          return fetchSubCategoryIconsSuccess(processedIcons);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch subcategory icons:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch subcategory icons';
          return of(fetchSubCategoryIconsFailure(errorMessage));
        })
      )
    )
  );

// Epic for fetching categories
export const fetchCategoriesEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchCategoriesStart.type, initializeCacheStart.type),
    filter(() => {
      const cache = state$.value.cache;
      return !cache.categoriesLoaded && !cache.categoriesLoading;
    }),
    mergeMap(() =>
      from(axiosInstance.get('/pennypal/api/v1/category/all')).pipe(
        map((response) => {
          console.log('✅ Categories fetched successfully:', response.data?.length || 0, 'items');
          return fetchCategoriesSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch categories:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch categories';
          return of(fetchCategoriesFailure(errorMessage));
        })
      )
    )
  );

// Epic for fetching subcategories
export const fetchSubcategoriesEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchSubcategoriesStart.type, initializeCacheStart.type),
    filter(() => {
      const cache = state$.value.cache;
      return !cache.subcategoriesLoaded && !cache.subcategoriesLoading;
    }),
    mergeMap(() =>
      from(axiosInstance.get('/pennypal/api/v1/subCategory/all')).pipe(
        map((response) => {
          console.log('✅ Subcategories fetched successfully:', response.data?.length || 0, 'items');
          return fetchSubcategoriesSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch subcategories:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch subcategories';
          return of(fetchSubcategoriesFailure(errorMessage));
        })
      )
    )
  );

// Combined cache epic
export const cacheEpic = merge(
  initializeCacheOnLoginEpic,
  fetchIconsEpic,
  fetchSubCategoryIconsEpic,
  fetchCategoriesEpic,
  fetchSubcategoriesEpic
);

export default cacheEpic;
