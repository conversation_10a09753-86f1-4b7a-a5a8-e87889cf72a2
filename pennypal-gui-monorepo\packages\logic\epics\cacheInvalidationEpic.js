import { ofType, combineEpics } from 'redux-observable';
import { of } from 'rxjs';
import { map, mergeMap } from 'rxjs/operators';
import {
  invalidateTransactionCache,
  invalidateRecurringTransactionCache,
  invalidateBudgetCache,
  invalidateAllTransactionRelatedCache,
  fetchTransactionsStart,
  fetchRecurringTransactionsStart,
  fetchFutureRecurringTransactionsStart,
  fetchBudgetSummaryStart,
  fetchBudgetDataStart
} from '../redux/cacheSlice';

// Epic to invalidate cache when transactions are added/modified/deleted
export const invalidateOnTransactionChangeEpic = (action$, state$) =>
  action$.pipe(
    ofType(
      // Transaction actions that should invalidate cache
      'transactions/addTransactionSuccess',
      'transactions/updateTransactionSuccess', 
      'transactions/deleteTransactionSuccess',
      'transactions/hideTransactionsSuccess',
      'transactions/unhideTransactionsSuccess',
      'transactions/bulkDeleteTransactionsSuccess',
      'transactions/importTransactionsSuccess',
      
      // Split transaction actions
      'splitTransactions/createSplitTransactionSuccess',
      'splitTransactions/updateSplitTransactionSuccess',
      'splitTransactions/deleteSplitTransactionSuccess',
      
      // Recurring transaction actions
      'recurringTransactions/addRecurringTransactionSuccess',
      'recurringTransactions/updateRecurringTransactionSuccess',
      'recurringTransactions/deleteRecurringTransactionSuccess'
    ),
    mergeMap((action) => {
      console.log('🔄 Transaction data changed, invalidating related cache:', action.type);
      
      // Invalidate transaction and budget related cache
      return of(invalidateAllTransactionRelatedCache());
    })
  );

// Epic to invalidate cache when accounts are synced
export const invalidateOnAccountSyncEpic = (action$, state$) =>
  action$.pipe(
    ofType(
      // Plaid/Account sync actions
      'plaid/syncAccountsSuccess',
      'plaid/linkAccountSuccess',
      'plaid/refreshAccountsSuccess',
      'accounts/syncAccountsSuccess',
      'accounts/refreshBalancesSuccess',
      'accounts/importTransactionsSuccess',
      
      // Bank connection actions
      'bankConnection/connectBankSuccess',
      'bankConnection/refreshConnectionSuccess',
      'bankConnection/syncTransactionsSuccess'
    ),
    mergeMap((action) => {
      console.log('🔄 Account sync completed, invalidating all transaction-related cache:', action.type);
      
      // When accounts are synced, new transactions may be imported
      // Invalidate all transaction and budget related cache
      return of(invalidateAllTransactionRelatedCache());
    })
  );

// Epic to invalidate cache when budget is created/modified
export const invalidateOnBudgetChangeEpic = (action$, state$) =>
  action$.pipe(
    ofType(
      // Budget actions that should invalidate cache
      'budget/addBudget',
      'budget/updateBudget', 
      'budget/deleteSubcategory',
      'budget/bulkUpdateBudgets',
      'budget/saveBudget',
      'budget/addBudgetItem',
      'budget/deleteSubcategoryBudget',
      
      // Budget creation/modification from API
      'budget/createBudgetSuccess',
      'budget/updateBudgetSuccess',
      'budget/deleteBudgetSuccess'
    ),
    mergeMap((action) => {
      console.log('🔄 Budget data changed, invalidating budget cache:', action.type);
      
      // Only invalidate budget-related cache, not transactions
      return of(invalidateBudgetCache());
    })
  );

// Epic to automatically refetch data after cache invalidation
export const refetchAfterInvalidationEpic = (action$, state$) =>
  action$.pipe(
    ofType(
      invalidateAllTransactionRelatedCache.type,
      invalidateTransactionCache.type,
      invalidateRecurringTransactionCache.type,
      invalidateBudgetCache.type
    ),
    mergeMap((action) => {
      console.log('🔄 Cache invalidated, triggering refetch:', action.type);
      
      const userId = state$.value?.auth?.user?.id;
      if (!userId) {
        console.warn('⚠️ User ID not found, skipping refetch');
        return of({ type: 'cache/refetchSkipped' });
      }
      
      const actions = [];
      
      // Determine what to refetch based on what was invalidated
      if (action.type === invalidateAllTransactionRelatedCache.type) {
        // Refetch everything
        actions.push(
          fetchTransactionsStart(),
          fetchRecurringTransactionsStart(),
          fetchFutureRecurringTransactionsStart(),
          fetchBudgetDataStart({ userId })
        );
        
        // Also refetch budget summary for current month
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth() + 1;
        actions.push(fetchBudgetSummaryStart({ userId, year: currentYear, month: currentMonth }));
        
      } else if (action.type === invalidateTransactionCache.type) {
        actions.push(fetchTransactionsStart());
        
      } else if (action.type === invalidateRecurringTransactionCache.type) {
        actions.push(
          fetchRecurringTransactionsStart(),
          fetchFutureRecurringTransactionsStart()
        );
        
      } else if (action.type === invalidateBudgetCache.type) {
        actions.push(fetchBudgetDataStart({ userId }));
        
        // Also refetch budget summary for current month
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth() + 1;
        actions.push(fetchBudgetSummaryStart({ userId, year: currentYear, month: currentMonth }));
      }
      
      // Return all refetch actions
      return actions.length > 0 ? of(...actions) : of({ type: 'cache/noRefetchNeeded' });
    })
  );

// Combined cache invalidation epic
export const cacheInvalidationEpic = combineEpics(
  invalidateOnTransactionChangeEpic,
  invalidateOnAccountSyncEpic,
  invalidateOnBudgetChangeEpic,
  refetchAfterInvalidationEpic
);

export default cacheInvalidationEpic;