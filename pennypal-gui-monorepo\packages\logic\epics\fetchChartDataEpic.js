import { ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { mergeMap, map, catchError } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchAccountData,
  fetchAccountDataSuccess,
  fetchAccountDataFailure
} from '../redux/accountChartSlice';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil'; // adjust path if needed

// Static mock data - only used when user has no accounts at all
const getStaticMockData = () => {
  const base = 2500;
  const trend = 300;
  const variance = 250;
  const months = [
    "Jan '24", "Feb '24", "Mar '24", "Apr '24", "May '24", "Jun '24",
    "Jul '24", "Aug '24", "Sep '24", "Oct '24", "Nov '24", "Dec '24"
  ];
  let data = [];
  for (let i = 0; i < months.length; i++) {
    data.push({
      name: months[i],
      balance: Math.round(base + i * trend + (Math.random() - 0.5) * variance),
      group_end_date: `2024-${String(i + 1).padStart(2, '0')}-01`,
      dateSortValue: new Date(`2024-${String(i + 1).padStart(2, '0')}-01`).getTime(),
      isMockData: true
    });
  }
  return data;
};

export const fetchChartDataEpic = (action$) => action$.pipe(
  ofType(fetchAccountData.type),
  mergeMap(action => {
    // Add null/undefined check and provide defaults
    const payload = action.payload || {};
    const { chartType = 'cash', timePeriod = 'yearly' } = payload;
    
    console.log('Epic received payload:', payload);
    console.log('Using chartType:', chartType, 'timePeriod:', timePeriod);
    
    const userId = getCurrentUserId();
    if (!userId) {
      return of(fetchAccountDataFailure({
        message: 'User not authenticated',
        status: 401,
        detail: 'JWT missing or invalid'
      }));
    }

    // Define period mapping
    const periodMapping = {
      'one-month': { x: 1, y: 7 },
      'three-month': { x: 3, y: 8 },
      'ytd': { x: 12, y: 15 },
      'half-year': { x: 6, y: 16 },
      'yearly': { x: 12, y: 30 },
      'quarterly-aggregate': { x: 12, y: 90 },
    };
  
    const { x, y } = periodMapping[timePeriod] || { x: 12, y: 30 };
  
    // Define account type mapping
    const accountTypeMapping = {
      'cash': 'depository',
      'creditCard': 'credit',
      'loan': 'loan',
      'investment': 'investment',
      'liability': 'liability',
      'networth': 'networth'
    };
  
    const accountType = accountTypeMapping[chartType] || 'depository';
    
    // Handle special cases for liability and networth
    if (chartType === 'liability') {
      // For liability, we need to fetch both loan and credit card data then combine them
      return from(Promise.all([
        axiosInstance.get(`/pennypal/api/v1/account/balances/agg/${userId}/${x}/${y}/loan`),
        axiosInstance.get(`/pennypal/api/v1/account/balances/agg/${userId}/${x}/${y}/credit`)
      ])).pipe(
        map(([loanResponse, creditCardResponse]) => {
          // Ensure responses contain arrays
          const loanData = Array.isArray(loanResponse.data) ? loanResponse.data : [];
          const creditData = Array.isArray(creditCardResponse.data) ? creditCardResponse.data : [];
          
          // If both are empty, return empty array (no mock data)
          if (loanData.length === 0 && creditData.length === 0) {
            return processChartData([], timePeriod, false);
          }
          
          // Use the longest array as the base to ensure we have all data points
          const baseData = loanData.length >= creditData.length ? loanData : creditData;
          const otherData = loanData.length >= creditData.length ? creditData : loanData;
          
          // If one dataset is empty, use the other
          if (baseData.length === 0) {
            return processChartData(otherData, timePeriod, false);
          }
          if (otherData.length === 0) {
            return processChartData(baseData, timePeriod, false);
          }
          
          // Combine loan and credit card data by matching dates
          const combinedData = baseData.map((baseItem, index) => {
            const baseDate = baseItem.group_end_date;
            
            // Find matching item in otherData by date, or use index as fallback
            let otherItem = otherData.find(item => item.group_end_date === baseDate);
            if (!otherItem && index < otherData.length) {
              otherItem = otherData[index];
            }
            
            return {
              group_end_date: baseDate,
              aggregated_balance: Number(baseItem.aggregated_balance || 0) + Number(otherItem?.aggregated_balance || 0)
            };
          });
          
          // Also handle any remaining items from otherData that weren't matched
          otherData.forEach(otherItem => {
            const otherDate = otherItem.group_end_date;
            const alreadyExists = combinedData.some(item => item.group_end_date === otherDate);
            
            if (!alreadyExists) {
              combinedData.push({
                group_end_date: otherDate,
                aggregated_balance: Number(otherItem.aggregated_balance || 0)
              });
            }
          });
          
          // Sort by date to maintain chronological order
          combinedData.sort((a, b) => new Date(a.group_end_date) - new Date(b.group_end_date));
          
          // Process combined data
          return processChartData(combinedData, timePeriod, false);
        }),
        map(processedData => fetchAccountDataSuccess(processedData)),
        catchError(error => {
          console.error('Error fetching liability data:', error);
          // Return empty data on error - no mock data for missing accounts
          return of(fetchAccountDataSuccess([]));
        })
      );
    } 
    else if (chartType === 'networth') {
      // For networth, we need to fetch multiple account types and calculate the total
      return from(Promise.all([
        axiosInstance.get(`/pennypal/api/v1/account/balances/agg/${userId}/${x}/${y}/depository`),
        axiosInstance.get(`/pennypal/api/v1/account/balances/agg/${userId}/${x}/${y}/investment`),
        axiosInstance.get(`/pennypal/api/v1/account/balances/agg/${userId}/${x}/${y}/loan`),
        axiosInstance.get(`/pennypal/api/v1/account/balances/agg/${userId}/${x}/${y}/credit`)
      ])).pipe(
        map(([cashResponse, investmentResponse, loanResponse, creditCardResponse]) => {
          // Ensure all responses contain arrays
          const cashData = Array.isArray(cashResponse.data) ? cashResponse.data : [];
          const investmentData = Array.isArray(investmentResponse.data) ? investmentResponse.data : [];
          const loanData = Array.isArray(loanResponse.data) ? loanResponse.data : [];
          const creditData = Array.isArray(creditCardResponse.data) ? creditCardResponse.data : [];
          
          // Check if all datasets are empty
          if (cashData.length === 0 && investmentData.length === 0 && 
              loanData.length === 0 && creditData.length === 0) {
            return processChartData([], timePeriod, false);
          }
          
          // Find the dataset with the most data points to use as our reference
          const dataSets = [cashData, investmentData, loanData, creditData];
          let maxLength = 0;
          let baseDataSet = [];
          
          dataSets.forEach(dataset => {
            if (dataset.length > maxLength) {
              maxLength = dataset.length;
              baseDataSet = dataset;
            }
          });
          
          // Handle empty data case
          if (baseDataSet.length === 0) {
            return processChartData([], timePeriod, false);
          }
          
          // Calculate net worth by combining data
          const netWorthData = baseDataSet.map((baseItem, index) => {
            // Find corresponding items from each response by matching dates
            const baseDate = baseItem.group_end_date;
            
            // Helper function to find matching item by date or get null
            const findMatchingItem = (dataset, targetDate) => {
              return dataset.find(item => item.group_end_date === targetDate) || 
                     { group_end_date: targetDate, aggregated_balance: 0 };
            };
            
            const cashItem = findMatchingItem(cashData, baseDate);
            const investmentItem = findMatchingItem(investmentData, baseDate);
            const loanItem = findMatchingItem(loanData, baseDate);
            const creditCardItem = findMatchingItem(creditData, baseDate);
            
            return {
              group_end_date: baseDate,
              aggregated_balance: 
                Number(cashItem.aggregated_balance || 0) + 
                Number(investmentItem.aggregated_balance || 0) - 
                Number(loanItem.aggregated_balance || 0) - 
                Number(creditCardItem.aggregated_balance || 0)
            };
          });
          
          // Process net worth data
          return processChartData(netWorthData, timePeriod, false);
        }),
        map(processedData => fetchAccountDataSuccess(processedData)),
        catchError(error => {
          console.error('Error fetching networth data:', error);
          // Return empty data on error - no mock data for missing accounts
          return of(fetchAccountDataSuccess([]));
        })
      );
    }
    else {
      // For regular account types, just fetch the data directly
      return from(axiosInstance.get(`/pennypal/api/v1/account/balances/agg/${userId}/${x}/${y}/${accountType}`)).pipe(
        map(response => {
          // Ensure response contains an array
          const responseData = Array.isArray(response.data) ? response.data : [];
          
          // Return empty array if no data - no mock data generation
          return processChartData(responseData, timePeriod, false);
        }),
        map(processedData => fetchAccountDataSuccess(processedData)),
        catchError(error => {
          console.error('Error fetching chart data:', error);
          // Return empty data on error - no mock data for missing accounts
          return of(fetchAccountDataSuccess([]));
        })
      );
    }
  })
);

// Helper function to process chart data
const processChartData = (responseData, timePeriod, isDummyData = false) => {
  // Safety check - handle empty data
  if (!responseData || responseData.length === 0) {
    return [];
  }
  
  // Get current date for YTD calculations
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();

  // Process the response data for all non-quarterly views
  if (timePeriod !== 'quarterly-aggregate') {
    return responseData.map(item => {
      if (!item || !item.group_end_date) {
        return null; // Skip invalid items
      }
      
      const date = new Date(item.group_end_date);
      let name;

      if (timePeriod === 'one-month' || timePeriod === 'three-month' || timePeriod === 'half-year') {
        name = `${date.getDate()} ${date.toLocaleString('en-US', { month: 'short' })}`;
      } else if (timePeriod === 'yearly') {
        name = date.toLocaleDateString('en-US', {
          month: 'short', 
          year: '2-digit'
        });
      } else if (timePeriod === 'ytd') {
        // Only process data from current year
        if (date.getFullYear() !== currentYear) {
          return null; // Skip this item
        }
        name = date.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric'
        });
      } else {
        name = date.toLocaleDateString('en-US', {
          day: 'numeric',
          month: 'short',
          year: '2-digit'
        });
      }

      return {
        name: name,
        balance: Number(item.aggregated_balance || 0),
        group_end_date: item.group_end_date,
        dateSortValue: date.getTime(),
        isDummyData: isDummyData
      };
    }).filter(item => item !== null); // Filter out null items
  } 
  // Special handling for quarterly view
  else {
    // Step 1: First aggregate data by quarters
    const quarterlyMap = {};
    
    responseData.forEach(item => {
      if (!item || !item.group_end_date) return; // Skip invalid items
      
      const date = new Date(item.group_end_date);
      const year = date.getFullYear();
      const month = date.getMonth();
      
      // Determine quarter (0-based months)
      let quarter;
      if (month <= 2) quarter = 1;      // Jan, Feb, Mar
      else if (month <= 5) quarter = 2;  // Apr, May, Jun
      else if (month <= 8) quarter = 3;  // Jul, Aug, Sep
      else quarter = 4;                  // Oct, Nov, Dec
      
      // Create a unique key for each quarter+year combination
      const quarterKey = `${year}-Q${quarter}`;
      
      if (!quarterlyMap[quarterKey]) {
        quarterlyMap[quarterKey] = {
          year: year,
          quarter: quarter,
          label: `Q${quarter} '${year.toString().slice(-2)}`,
          balances: [Number(item.aggregated_balance || 0)],
          sortValue: year * 10 + quarter  // For sorting
        };
      } else {
        // Add this balance to the existing quarter
        quarterlyMap[quarterKey].balances.push(Number(item.aggregated_balance || 0));
      }
    });
    
    // Step 2: Calculate average balance for each quarter
    let quarters = Object.values(quarterlyMap).map(quarter => {
      // Calculate average balance for this quarter
      const sum = quarter.balances.reduce((acc, val) => acc + val, 0);
      const avgBalance = quarter.balances.length > 0 ? sum / quarter.balances.length : 0;
      
      return {
        name: quarter.label,
        balance: avgBalance,
        sortValue: quarter.sortValue, 
        isDummyData: isDummyData
      };
    });
    
    // Safety check - handle empty quarters
    if (quarters.length === 0) {
      return [];
    }
    
    // Step 3: Sort quarters in chronological order, most recent first
    quarters.sort((a, b) => b.sortValue - a.sortValue);
    
    // Step 4: Take only the most recent 4 quarters for display
    quarters = quarters.slice(0, 4);
    
    return quarters;
  }
};

export default [fetchChartDataEpic];