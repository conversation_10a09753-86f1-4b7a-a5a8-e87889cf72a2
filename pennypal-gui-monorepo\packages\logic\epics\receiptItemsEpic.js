import { ofType } from 'redux-observable';
import { of, from } from 'rxjs';
import { switchMap, catchError } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchReceiptItemsRequest,
  fetchReceiptItemsSuccess,
  fetchReceiptItemsFailure,
  fetchItemSummarySuccess,
  fetchItemSummaryFailure,
  fetchItemSummaryRequest
} from '../redux/receiptItemsSlice';

export const fetchReceiptItemsEpic = (action$) =>
  action$.pipe(
    ofType(fetchReceiptItemsRequest.type),
    switchMap((action) => {
      const { receiptId } = action.payload || {};
      const url = receiptId
        ? `/pennypal/api/v1/receipt-items/by-receipt/${receiptId}`
        : `/pennypal/api/v1/receipt-items/all`;

      console.log('Fetching from:', url);

      return from(axiosInstance.get(url)).pipe(
        switchMap((response) => of(fetchReceiptItemsSuccess(response.data))),
        catchError((error) =>
          of(fetchReceiptItemsFailure(error.response?.data || 'Failed to fetch receipt items'))
        )
      );
    })
  );
export const fetchItemSummaryEpic = (action$) =>
  action$.pipe(
    ofType(fetchItemSummaryRequest.type),
    switchMap(() =>
      from(axiosInstance.get('/pennypal/api/v1/receipt/summary')).pipe(
        switchMap((response) => of(fetchItemSummarySuccess(response.data))),
        catchError((error) =>
          of(fetchItemSummaryFailure(error.response?.data || 'Failed to fetch item summary'))
        )
      )
    )
  );