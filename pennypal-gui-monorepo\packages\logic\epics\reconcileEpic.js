import { ofType } from 'redux-observable';
import { of, from } from 'rxjs';
import { switchMap, catchError } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchReconcileRequest,
  fetchReconcileSuccess,
  fetchReconcileFailure,
} from '../redux/reconcileSlice';

export const fetchReconcileEpic = (action$) =>
  action$.pipe(
    ofType(fetchReconcileRequest.type),
    switchMap(() =>
      from(axiosInstance.get('/pennypal/api/v1/reconcile/all')).pipe(
        switchMap((response) => of(fetchReconcileSuccess(response.data))),
        catchError((error) => {
          console.error('Fetch reconcile error:', error);
          return of(fetchReconcileFailure(error.response?.data || 'Failed to fetch reconciled transactions'));
        })
      )
    )
  );