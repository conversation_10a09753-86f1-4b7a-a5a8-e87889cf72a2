
// spendingDashboardEpics.js
import { combineEpics, ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { mergeMap, catchError, map, startWith } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchExpensesStart,
  fetchMonthlyExpensesSuccess,
  fetchYearlyExpensesSuccess,
  fetchExpensesFailure,
} from '../redux/spendingDashboardSlice';
import {getCurrentUserId } from '../../web/src/utils/AuthUtil';

// Updated action creators - no longer need userId parameter
export const fetchMonthlyExpenses = () => ({
  type: 'spendingDashboard/fetchMonthlyExpenses',
});

export const fetchYearlyExpenses = () => ({
  type: 'spendingDashboard/fetchYearlyExpenses',
});

// Epic for fetching monthly expenses
const fetchMonthlyExpensesEpic = (action$) =>
  action$.pipe(
    ofType('spendingDashboard/fetchMonthlyExpenses'),
    mergeMap((action) => {
      const userId = getCurrentUserId(); // Get userId from token
      
      // Check if user is authenticated
      if (!userId) {
        return of(fetchExpensesFailure('User not authenticated'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/budget/user/${userId}/month`)).pipe(
        map((response) => {
          console.log('📡 Monthly API response:', response.data);
          return fetchMonthlyExpensesSuccess(response.data);
        }),
        catchError((error) => {
          console.error('Error fetching monthly expenses:', error);
          return of(fetchExpensesFailure(error.response?.data || 'Failed to fetch monthly expenses'));
        }),
        startWith(fetchExpensesStart())
      );
    })
  );

// Epic for fetching yearly expenses
const fetchYearlyExpensesEpic = (action$) =>
  action$.pipe(
    ofType('spendingDashboard/fetchYearlyExpenses'),
    mergeMap((action) => {
      const userId = getCurrentUserId(); // Get userId from token
      
      // Check if user is authenticated
      if (!userId) {
        return of(fetchExpensesFailure('User not authenticated'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/budget/user/${userId}/year`)).pipe(
        map((response) => {
          console.log('📡 Yearly API response:', response.data);
          return fetchYearlyExpensesSuccess(response.data);
        }),
        catchError((error) => {
          console.error('Error fetching yearly expenses:', error);
          return of(fetchExpensesFailure(error.response?.data || 'Failed to fetch yearly expenses'));
        }),
        startWith(fetchExpensesStart())
      );
    })
  );

// Combine all spending dashboard related epics
export const spendingDashboardEpics = combineEpics(
  fetchMonthlyExpensesEpic,
  fetchYearlyExpensesEpic
);