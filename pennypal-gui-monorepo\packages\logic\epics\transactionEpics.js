import { combineEpics } from 'redux-observable';
import { ofType } from 'redux-observable';
import { mergeMap, map, catchError, switchMap, tap,timeout } from 'rxjs/operators';
import { from, of } from 'rxjs';
import {
  fetchTransactionsStart,
  fetchTransactionSummaryStart,
  fetchTransactionsSuccess,
  fetchTransactionsFailure,
  addTransactionSuccess,
  toggleAddTransactionModal,
  resetNewTransaction,
  fetchTransactionSummarySuccess,
  fetchTransactionSummaryFailure,
  hideFromBudgetSuccess,
  hideFromBudgetFailure,
  fetchAllIconsSuccess,
  fetchAllIconsFailure
} from '../redux/transactionSlice';
import { axiosInstance } from '../api/axiosConfig';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';


// Enhanced logging function
const logEpicAction = (epicName, stage, data) => {
  const timestamp = new Date().toISOString();
  console.group(`🔄 ${epicName} - ${stage} (${timestamp})`);
  if (data) {
    if (data instanceof Error) {
      console.error('Error:', data.message);
      console.error('Stack:', data.stack);
    } else {
      console.log('Data:', data);
    }
  }
  console.groupEnd();
};

// Epic for fetching paginated transactions
const fetchTransactionsEpic = (action$, state$) => action$.pipe(
  ofType("transactions/fetchTransactionsStart"),
  tap(() => console.log('fetchTransactionsEpic: Starting fetch')),
  mergeMap(() => {
    const userId = getCurrentUserId();
    const { page, pageSize } = state$.value.transactions;
    console.log('fetchTransactionsEpic: Fetching with page:', page, 'pageSize:', pageSize);

    return from(
      axiosInstance.get(`/pennypal/api/v1/transaction/transactions/user/${userId}?page=${page}&pageSize=${pageSize}`)
    ).pipe(
      timeout(60000), // 10 seconds
      tap(response => console.log('fetchTransactionsEpic: API Response', response.data)),
      map(response => {
        const data = response.data;
        return fetchTransactionsSuccess({
          content: data.transactions || data.content || [],
          totalElements: data.totalElements || data.total || 0,
          totalPages: data.totalPages || Math.ceil((data.totalElements || 0) / state$.value.transactions.pageSize),
         append: state$.value.transactions.appendMode // NEW
        });
      }),
      catchError(error => {
        console.error('fetchTransactionsEpic: Error', error);
        let message = 'Failed to fetch transactions';
        if (error.name === 'TimeoutError') {
          message = 'Request timed out. Please try again.';
        } else if (error.response?.data?.message) {
          message = error.response.data.message;
        }
        return of(fetchTransactionsFailure(message));
      })
    );
  })
);

// Epic for adding a new transaction
const addTransactionEpic = (action$, state$) => action$.pipe(
  ofType('transactions/addTransaction'),
  tap(action => logEpicAction('addTransactionEpic', 'Starting transaction add', action)),
 switchMap((action) => {
  const { transactionData } = action.payload;

  const parsedDate = new Date(transactionData.transactionDate);
  if (isNaN(parsedDate)) {
    logEpicAction('addTransactionEpic', 'Invalid date format in transactionData', transactionData.transactionDate);
    return of(fetchTransactionsFailure('Invalid date format provided'));
  }

  const formattedTransaction = {
    transactionDate: parsedDate.toISOString(),
    description: transactionData.description,
    category: transactionData.category,
    categoryId: transactionData.categoryId || null,
  accountId: transactionData.accountId || null,
      tax: parseFloat(transactionData.tax) || 0,
      notes: transactionData.notes || '',
      tag: transactionData.tag || '',
      hideFromBudget: transactionData.hideFromBudget || false,
      hidden: transactionData.hidden || false,
      userId: transactionData.userId || getCurrentUserId(),
    transactionAmount: parseFloat(transactionData.transactionAmount) || 0
  };

  logEpicAction('addTransactionEpic', 'Formatted Transaction', formattedTransaction);

  return from(axiosInstance.post('/pennypal/api/v1/transaction/add', formattedTransaction)).pipe(
        tap(response => logEpicAction('addTransactionEpic', 'API Response', response.data)),
      map(response => addTransactionSuccess(response.data)),
        catchError(error => {
          logEpicAction('addTransactionEpic', 'Error', error);
          return of(fetchTransactionsFailure(error.response?.data?.message || 'Failed to add transaction'));
        })
      );
    })
  );

// Epic for getting transaction details
const getTransactionDetailsEpic = (action$) => action$.pipe(
  ofType('transactions/getTransactionDetails'),
  tap(action => logEpicAction('getTransactionDetailsEpic', 'Starting fetch details', action)),
  mergeMap(action =>
    from(axiosInstance.get(`/pennypal/api/v1/transaction/${action.payload}`)).pipe(
      tap(response => logEpicAction('getTransactionDetailsEpic', 'API Response', response.data)),
      map(response => ({
        type: 'transactions/getTransactionDetailsSuccess',
        payload: response.data
      })),
      catchError(error => {
        logEpicAction('getTransactionDetailsEpic', 'Error', error);
        return of({
          type: 'transactions/getTransactionDetailsFailure',
          payload: error.response?.data?.message || 'Failed to fetch transaction details'
        });
      })
    )
  )
);

// Epic for updating a transaction
// In your epics file
const updateTransactionEpic = (action$) => action$.pipe(
  ofType('transactions/updateTransaction'),
  tap(action => logEpicAction('updateTransactionEpic', 'Starting update', action)),
  mergeMap(action => {
    const { id, data } = action.payload;

     const payload = {
    transactionDate: data.transactionDate,
      description: data.description,
      category: data.category,
      account: data.account,
      transactionAmount: parseFloat(data.transactionAmount) || 0,
      tax: parseFloat(data.tax) || 0,
      notes: data.notes || '',
      tag: data.tag || '',
      hideFromBudget: data.hideFromBudget || false,
      hidden: data.hidden || false,
      userId: data.userId || getCurrentUserId(),
    };

    // Debug: log payload before sending
    console.log('PUT request data:', payload);
    
    return from(axiosInstance.put(`/pennypal/api/v1/transaction/${id}`, data)).pipe(
       timeout(50000),
      tap(response => logEpicAction('updateTransactionEpic', 'API Response', response.data)),
      map(response => ({
        type: 'transactions/updateTransactionSuccess',
       payload: {
          transaction_id: response.data.id,
          date: response.data.transactionDate,
          name: response.data.description,
          subcategory: response.data.category,
          bank: response.data.account,
          amount: response.data.transactionAmount,
          tax: response.data.tax,
          notes: response.data.notes,
          tag: response.data.tag,
          hideFromBudget: response.data.hideFromBudget,
          hidden: response.data.hidden,
        },
      })),
      catchError(error => {
        logEpicAction('updateTransactionEpic', 'Error', error);
        return of({
          type: 'transactions/updateTransactionFailure',
          payload: error.response?.data?.message || 'Failed to update transaction'
        });
      })
    );
  })
);

const hideFromBudgetEpic = (action$) => action$.pipe(
  ofType('transactions/hideFromBudget'),
  tap(action => logEpicAction('hideFromBudgetEpic', 'Starting hide from budget', action)),
  switchMap(action => {
    const transactionIds = action.payload;
    console.log('Sending raw array:', transactionIds);
    return from(
      axiosInstance.post('/pennypal/api/v1/transaction/hideFromBudget', transactionIds)
    ).pipe(
      tap(response => logEpicAction('hideFromBudgetEpic', 'API Response', response.data)),
      map(() => hideFromBudgetSuccess(transactionIds)),
      catchError(error =>
        of(hideFromBudgetFailure(error.response?.data?.error || 'Failed to hide transactions from budget'))
      )
    );
  })
);



// Epic for deleting a transaction
const deleteTransactionEpic = (action$) => action$.pipe(
  ofType('transactions/deleteTransaction'),
  tap(action => logEpicAction('deleteTransactionEpic', 'Starting delete', action)),
  mergeMap(action => {
    const transactionId = action.payload;
    return from(axiosInstance.post(`/pennypal/api/v1/transaction/${transactionId}`)).pipe(
      tap(response => logEpicAction('deleteTransactionEpic', 'API Response', response.data)),
      map(response => ({
        type: 'transactions/deleteTransactionSuccess',
        payload: {
          id: transactionId,
           isRemove: response.data.isRemove || true     
        }
      })),
      catchError(error => {
        logEpicAction('deleteTransactionEpic', 'Error', error);
        return of({
          type: 'transactions/deleteTransactionFailure',
          payload: {
            message: error.response?.data?.error || error.message || 'Failed to delete transaction',
            transactionId: transactionId
          }
        });
      })
    );
  })
);

const fetchHiddenTransactionsEpic = (action$) =>
  action$.pipe(
    ofType('transactions/fetchHiddenTransactions'),
    tap(() => console.log("Fetching hidden transactions...")),
    mergeMap(() => {
      const userId = getCurrentUserId();

      return from(
        axiosInstance.get(`/pennypal/api/v1/transaction/hidden/user/${userId}`)
      ).pipe(
        map((response) => ({
          type: 'transactions/fetchHiddenTransactionsSuccess',
          payload: response.data,
        })),
        catchError((error) =>
          of({
            type: 'transactions/fetchHiddenTransactionsFailure',
            payload: error.message || 'Failed to fetch hidden transactions',
          })
        )
      );
    })
  );

  const hideTransactionsEpic = (action$) => action$.pipe(
  ofType('transactions/hideTransactions'),
  tap(action => logEpicAction('hideTransactionsEpic', 'Starting hide transactions', action)),
  mergeMap(action => {
    const transactionIds = action.payload;

    if (!Array.isArray(transactionIds) || transactionIds.length === 0) {
      return of({
        type: 'transactions/hideTransactionsFailure',
        payload: 'No transactions selected or invalid payload'
      });
    }

    console.log('Sending to backend:', { transactionIds });

    return from(
      axiosInstance.post('/pennypal/api/v1/transaction/hide', { transactionIds },
        {
          timeout: 50000
        }
      ) ,
    ).pipe(
      tap(response => logEpicAction('hideTransactionsEpic', 'API Response', response?.data)),
      map(() => ({
        type: 'transactions/hideTransactionsSuccess',
        payload: transactionIds
      })),
      catchError(error => {
        const errorMsg = error?.response?.data || 
                         error?.message || 
                         'Failed to hide transactions';
        logEpicAction('hideTransactionsEpic', 'Error', errorMsg);
        console.error('API Error:', error?.response || error);
        return of({
          type: 'transactions/hideTransactionsFailure',
          payload: errorMsg
        });
      })
    );
  })
);

const getReconciledTransactionsEpic = (action$) => action$.pipe(
  ofType('transactions/getReconciledTransactions'),
  tap(action => console.log('EPIC: Fetching for reconcileId:', action.payload)),
  switchMap(action => {
    const url = `/pennypal/api/v1/reconcile/by-reconcile-id/${action.payload}`;
    console.log('EPIC: Making request to:', url);
    
    return from(axiosInstance.get(url)).pipe(
      tap(response => console.log('EPIC: Received response:', response.data)),
      map(response => ({
        type: 'transactions/getReconciledTransactionsSuccess',
        payload: {
          reconcileId: action.payload,
          transactions: response.data
        }
      })),
      catchError(error => {
        console.error('EPIC: Error fetching reconciled transactions:', error);
        return of({
          type: 'transactions/getReconciledTransactionsFailure',
          payload: error.message
        });
      })
    );
  })
);

const fetchTransactionSummaryEpic = (action$) => action$.pipe(
  ofType("transactions/fetchTransactionSummaryStart"),
  mergeMap(() => {
    const userId = getCurrentUserId();
      console.log("Fetching transaction summary for user", userId); 
    return from(axiosInstance.get(`/pennypal/api/v1/transaction/summary/user/${userId}`)).pipe(
      map(response => fetchTransactionSummarySuccess(response.data)),
      catchError(error =>
        of(fetchTransactionSummaryFailure(error.response?.data?.message || "Failed to fetch summary"))
      )
    );
  })
);

const fetchCategoryMonthlyExpensesEpic = (action$, state$) => 
  action$.pipe(
    ofType('transactions/fetchCategoryMonthlyExpensesStart'),
    mergeMap(action => {
      const { categoryId, subCategoryId, userId, months = 6 } = action.payload;
      
      // Validate inputs
      if (!categoryId || !userId) {
        return EMPTY;
      }

      return from(
        axiosInstance.get(`/pennypal/api/v1/transaction/category/summary`, {
          params: {
            categoryId,
            subCategoryId,
            userId,
            months
          }
        })
      ).pipe(
        map(response => ({
          type: 'transactions/fetchCategoryMonthlyExpensesSuccess',
          payload: response.data.summary || [],
        })),
        catchError(error => of({
          type: 'transactions/fetchCategoryMonthlyExpensesFailure',
          payload: error.message,
        }))
      );
    })
  );

export const fetchAllSubCategoryIconsEpic = (action$) => 
  action$.pipe(
    ofType('transactions/fetchAllIconsStart'),
    mergeMap(() =>
      from(axiosInstance.get('/pennypal/api/v1/subCategory/icons')).pipe(
        map(response => {
          if (!response.data || !Array.isArray(response.data)) {
            console.error('Invalid response format:', response);
            return fetchAllIconsFailure('Invalid response format from server');
          }

          const iconMap = {};
          const validIcons = response.data.filter(dto => 
            dto.subCategoryId && dto.base64Icon
          );

        validIcons.forEach(dto => {
  try {
    const base64 = dto.base64Icon;
    if (!base64) return;

    // Generate data URL for <img src="...">
    iconMap[dto.subCategoryId] = `data:image/png;base64,${base64}`;
  } catch (error) {
    console.error('Error creating icon URL for subCategoryId:', dto.subCategoryId, error);
  }
});


          console.log('Successfully mapped icons:', Object.keys(iconMap).length);
          return fetchAllIconsSuccess({
            icons: validIcons,
            iconMap
          });
        }),
        catchError(error => {
          console.error('API Error fetching icons:', error);
          return of(fetchAllIconsFailure(error.message));
        })
      )
    )
  );
export const rootTransactionEpic = combineEpics(
  fetchTransactionsEpic,
  addTransactionEpic,
  getTransactionDetailsEpic,
  updateTransactionEpic,
  deleteTransactionEpic,
  hideTransactionsEpic,
  getReconciledTransactionsEpic,
  fetchHiddenTransactionsEpic,
  fetchTransactionSummaryEpic,
  hideFromBudgetEpic,
  fetchCategoryMonthlyExpensesEpic,
  fetchAllSubCategoryIconsEpic
);

export default rootTransactionEpic;
