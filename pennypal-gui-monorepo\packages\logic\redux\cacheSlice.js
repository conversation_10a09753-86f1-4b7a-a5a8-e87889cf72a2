import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // Icons data from api/icons/list
  icons: [],
  iconsLoaded: false,
  iconsLoading: false,
  iconsError: null,
  iconMap: {},

  // Subcategory icons from api/v1/subCategory/icons  
  subCategoryIcons: [],
  subCategoryIconsLoaded: false,
  subCategoryIconsLoading: false,
  subCategoryIconsError: null,
  subCategoryIconMap: {},

  // Categories from api/v1/category/all
  categories: [],
  categoriesLoaded: false,
  categoriesLoading: false,
  categoriesError: null,

  // Subcategories from api/v1/subCategory/all
  subcategories: [],
  subcategoriesLoaded: false,
  subcategoriesLoading: false,
  subcategoriesError: null,

  // Transactions from api/v1/transaction/transactions/user/{userId}
  transactions: [],
  transactionsLoaded: false,
  transactionsLoading: false,
  transactionsError: null,
  transactionsPagination: { page: 0, pageSize: 250, totalElements: 0, totalPages: 0 },

  // Recurring transactions from api/v1/transaction/recurring/fetch2/{userId}
  recurringTransactions: [],
  recurringTransactionsLoaded: false,
  recurringTransactionsLoading: false,
  recurringTransactionsError: null,

  // Future recurring transactions from api/v1/transaction/recurring/fetch_future/{userId}
  futureRecurringTransactions: [],
  futureRecurringTransactionsLoaded: false,
  futureRecurringTransactionsLoading: false,
  futureRecurringTransactionsError: null,

  // Budget summary from api/v1/budget/summary_by_month/{userId}/{year}/{month}
  budgetSummary: null,
  budgetSummaryLoaded: false,
  budgetSummaryLoading: false,
  budgetSummaryError: null,
  budgetSummaryParams: { userId: null, year: null, month: null },

  // Budget data from api/v1/budget/user/{userId}/month
  budgetData: [],
  budgetDataLoaded: false,
  budgetDataLoading: false,
  budgetDataError: null,
  budgetDataParams: { userId: null },

  // Cache metadata
  lastUpdated: null,
  cacheVersion: '1.0.0'
};

const cacheSlice = createSlice({
  name: 'cache',
  initialState,
  reducers: {
    // Icons actions
    fetchIconsStart: (state) => {
      state.iconsLoading = true;
      state.iconsError = null;
    },
    fetchIconsSuccess: (state, action) => {
      state.icons = action.payload;
      state.iconsLoaded = true;
      state.iconsLoading = false;
      state.iconsError = null;
      
      // Build icon map for faster lookup
      const iconMap = {};
      action.payload.forEach(icon => {
        if (icon.iconName && icon.svgContent) {
          iconMap[icon.iconName.toLowerCase()] = icon.svgContent;
        }
      });
      state.iconMap = iconMap;
      state.lastUpdated = Date.now();
    },
    fetchIconsFailure: (state, action) => {
      state.iconsLoading = false;
      state.iconsError = action.payload;
    },

    // Subcategory icons actions
    fetchSubCategoryIconsStart: (state) => {
      state.subCategoryIconsLoading = true;
      state.subCategoryIconsError = null;
    },
    fetchSubCategoryIconsSuccess: (state, action) => {
      state.subCategoryIcons = action.payload;
      state.subCategoryIconsLoaded = true;
      state.subCategoryIconsLoading = false;
      state.subCategoryIconsError = null;
      
      // Build subcategory icon map
      const iconMap = {};
      action.payload.forEach(dto => {
        if (dto.subCategoryId && dto.base64Icon) {
          try {
            const base64 = dto.base64Icon;
            // Generate data URL for <img src="...">
            iconMap[dto.subCategoryId] = `data:image/png;base64,${base64}`;
          } catch (error) {
            console.error(`Failed to process icon for subcategory ${dto.subCategoryId}:`, error);
          }
        }
      });
      state.subCategoryIconMap = iconMap;
      state.lastUpdated = Date.now();
    },
    fetchSubCategoryIconsFailure: (state, action) => {
      state.subCategoryIconsLoading = false;
      state.subCategoryIconsError = action.payload;
    },

    // Categories actions
    fetchCategoriesStart: (state) => {
      state.categoriesLoading = true;
      state.categoriesError = null;
    },
    fetchCategoriesSuccess: (state, action) => {
      state.categories = action.payload;
      state.categoriesLoaded = true;
      state.categoriesLoading = false;
      state.categoriesError = null;
      state.lastUpdated = Date.now();
    },
    fetchCategoriesFailure: (state, action) => {
      state.categoriesLoading = false;
      state.categoriesError = action.payload;
    },

    // Subcategories actions
    fetchSubcategoriesStart: (state) => {
      state.subcategoriesLoading = true;
      state.subcategoriesError = null;
    },
    fetchSubcategoriesSuccess: (state, action) => {
      state.subcategories = action.payload;
      state.subcategoriesLoaded = true;
      state.subcategoriesLoading = false;
      state.subcategoriesError = null;
      state.lastUpdated = Date.now();
    },
    fetchSubcategoriesFailure: (state, action) => {
      state.subcategoriesLoading = false;
      state.subcategoriesError = action.payload;
    },

    // Transactions actions
    fetchTransactionsStart: (state) => {
      state.transactionsLoading = true;
      state.transactionsError = null;
    },
    fetchTransactionsSuccess: (state, action) => {
      state.transactions = action.payload.content || action.payload;
      state.transactionsPagination = {
        page: action.payload.page || 0,
        pageSize: action.payload.pageSize || 250,
        totalElements: action.payload.totalElements || 0,
        totalPages: action.payload.totalPages || 0
      };
      state.transactionsLoaded = true;
      state.transactionsLoading = false;
      state.transactionsError = null;
      state.lastUpdated = Date.now();
    },
    fetchTransactionsFailure: (state, action) => {
      state.transactionsLoading = false;
      state.transactionsError = action.payload;
    },

    // Recurring transactions actions
    fetchRecurringTransactionsStart: (state) => {
      state.recurringTransactionsLoading = true;
      state.recurringTransactionsError = null;
    },
    fetchRecurringTransactionsSuccess: (state, action) => {
      state.recurringTransactions = action.payload;
      state.recurringTransactionsLoaded = true;
      state.recurringTransactionsLoading = false;
      state.recurringTransactionsError = null;
      state.lastUpdated = Date.now();
    },
    fetchRecurringTransactionsFailure: (state, action) => {
      state.recurringTransactionsLoading = false;
      state.recurringTransactionsError = action.payload;
    },

    // Future recurring transactions actions
    fetchFutureRecurringTransactionsStart: (state) => {
      state.futureRecurringTransactionsLoading = true;
      state.futureRecurringTransactionsError = null;
    },
    fetchFutureRecurringTransactionsSuccess: (state, action) => {
      state.futureRecurringTransactions = action.payload;
      state.futureRecurringTransactionsLoaded = true;
      state.futureRecurringTransactionsLoading = false;
      state.futureRecurringTransactionsError = null;
      state.lastUpdated = Date.now();
    },
    fetchFutureRecurringTransactionsFailure: (state, action) => {
      state.futureRecurringTransactionsLoading = false;
      state.futureRecurringTransactionsError = action.payload;
    },

    // Budget summary actions
    fetchBudgetSummaryStart: (state, action) => {
      state.budgetSummaryLoading = true;
      state.budgetSummaryError = null;
      if (action.payload) {
        state.budgetSummaryParams = action.payload;
      }
    },
    fetchBudgetSummarySuccess: (state, action) => {
      state.budgetSummary = action.payload;
      state.budgetSummaryLoaded = true;
      state.budgetSummaryLoading = false;
      state.budgetSummaryError = null;
      state.lastUpdated = Date.now();
    },
    fetchBudgetSummaryFailure: (state, action) => {
      state.budgetSummaryLoading = false;
      state.budgetSummaryError = action.payload;
    },

    // Budget data actions
    fetchBudgetDataStart: (state, action) => {
      state.budgetDataLoading = true;
      state.budgetDataError = null;
      if (action.payload) {
        state.budgetDataParams = action.payload;
      }
    },
    fetchBudgetDataSuccess: (state, action) => {
      state.budgetData = action.payload;
      state.budgetDataLoaded = true;
      state.budgetDataLoading = false;
      state.budgetDataError = null;
      state.lastUpdated = Date.now();
    },
    fetchBudgetDataFailure: (state, action) => {
      state.budgetDataLoading = false;
      state.budgetDataError = action.payload;
    },

    // Clear cache action
    clearCache: (state) => {
      // Revoke object URLs to prevent memory leaks
      Object.values(state.subCategoryIconMap).forEach(url => {
        try {
          URL.revokeObjectURL(url);
        } catch (error) {
          console.warn('Failed to revoke object URL:', error);
        }
      });

      return initialState;
    },

    // Selective cache invalidation actions
    invalidateTransactionCache: (state) => {
      console.log('🗑️ Invalidating transaction cache');
      state.transactions = [];
      state.transactionsLoaded = false;
      state.transactionsLoading = false;
      state.transactionsError = null;
      state.transactionsPagination = { page: 0, pageSize: 250, totalElements: 0, totalPages: 0 };
    },

    invalidateRecurringTransactionCache: (state) => {
      console.log('🗑️ Invalidating recurring transaction cache');
      state.recurringTransactions = [];
      state.recurringTransactionsLoaded = false;
      state.recurringTransactionsLoading = false;
      state.recurringTransactionsError = null;

      state.futureRecurringTransactions = [];
      state.futureRecurringTransactionsLoaded = false;
      state.futureRecurringTransactionsLoading = false;
      state.futureRecurringTransactionsError = null;
    },

    invalidateBudgetCache: (state) => {
      console.log('🗑️ Invalidating budget cache');
      state.budgetSummary = null;
      state.budgetSummaryLoaded = false;
      state.budgetSummaryLoading = false;
      state.budgetSummaryError = null;
      state.budgetSummaryParams = { userId: null, year: null, month: null };

      state.budgetData = [];
      state.budgetDataLoaded = false;
      state.budgetDataLoading = false;
      state.budgetDataError = null;
      state.budgetDataParams = { userId: null };
    },

    invalidateAllTransactionRelatedCache: (state) => {
      console.log('🗑️ Invalidating all transaction and budget related cache');
      // Invalidate transactions
      state.transactions = [];
      state.transactionsLoaded = false;
      state.transactionsLoading = false;
      state.transactionsError = null;
      state.transactionsPagination = { page: 0, pageSize: 250, totalElements: 0, totalPages: 0 };

      // Invalidate recurring transactions
      state.recurringTransactions = [];
      state.recurringTransactionsLoaded = false;
      state.recurringTransactionsLoading = false;
      state.recurringTransactionsError = null;

      state.futureRecurringTransactions = [];
      state.futureRecurringTransactionsLoaded = false;
      state.futureRecurringTransactionsLoading = false;
      state.futureRecurringTransactionsError = null;

      // Invalidate budget data
      state.budgetSummary = null;
      state.budgetSummaryLoaded = false;
      state.budgetSummaryLoading = false;
      state.budgetSummaryError = null;
      state.budgetSummaryParams = { userId: null, year: null, month: null };

      state.budgetData = [];
      state.budgetDataLoaded = false;
      state.budgetDataLoading = false;
      state.budgetDataError = null;
      state.budgetDataParams = { userId: null };
    },

    // Initialize cache from login
    initializeCacheStart: () => {
      // Don't set loading states here - let individual epics handle their own loading states
      // This action just triggers the cache initialization process
      console.log('Cache initialization triggered');
    }
  }
});

export const {
  fetchIconsStart,
  fetchIconsSuccess,
  fetchIconsFailure,
  fetchSubCategoryIconsStart,
  fetchSubCategoryIconsSuccess,
  fetchSubCategoryIconsFailure,
  fetchCategoriesStart,
  fetchCategoriesSuccess,
  fetchCategoriesFailure,
  fetchSubcategoriesStart,
  fetchSubcategoriesSuccess,
  fetchSubcategoriesFailure,
  fetchTransactionsStart,
  fetchTransactionsSuccess,
  fetchTransactionsFailure,
  fetchRecurringTransactionsStart,
  fetchRecurringTransactionsSuccess,
  fetchRecurringTransactionsFailure,
  fetchFutureRecurringTransactionsStart,
  fetchFutureRecurringTransactionsSuccess,
  fetchFutureRecurringTransactionsFailure,
  fetchBudgetSummaryStart,
  fetchBudgetSummarySuccess,
  fetchBudgetSummaryFailure,
  fetchBudgetDataStart,
  fetchBudgetDataSuccess,
  fetchBudgetDataFailure,
  clearCache,
  invalidateTransactionCache,
  invalidateRecurringTransactionCache,
  invalidateBudgetCache,
  invalidateAllTransactionRelatedCache,
  initializeCacheStart
} = cacheSlice.actions;

export default cacheSlice.reducer;