import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // Icons data from api/icons/list
  icons: [],
  iconsLoaded: false,
  iconsLoading: false,
  iconsError: null,
  iconMap: {},

  // Subcategory icons from api/v1/subCategory/icons  
  subCategoryIcons: [],
  subCategoryIconsLoaded: false,
  subCategoryIconsLoading: false,
  subCategoryIconsError: null,
  subCategoryIconMap: {},

  // Categories from api/v1/category/all
  categories: [],
  categoriesLoaded: false,
  categoriesLoading: false,
  categoriesError: null,

  // Subcategories from api/v1/subCategory/all
  subcategories: [],
  subcategoriesLoaded: false,
  subcategoriesLoading: false,
  subcategoriesError: null,

  // Cache metadata
  lastUpdated: null,
  cacheVersion: '1.0.0'
};

const cacheSlice = createSlice({
  name: 'cache',
  initialState,
  reducers: {
    // Icons actions
    fetchIconsStart: (state) => {
      state.iconsLoading = true;
      state.iconsError = null;
    },
    fetchIconsSuccess: (state, action) => {
      state.icons = action.payload;
      state.iconsLoaded = true;
      state.iconsLoading = false;
      state.iconsError = null;
      
      // Build icon map for faster lookup
      const iconMap = {};
      action.payload.forEach(icon => {
        if (icon.iconName && icon.svgContent) {
          iconMap[icon.iconName.toLowerCase()] = icon.svgContent;
        }
      });
      state.iconMap = iconMap;
      state.lastUpdated = Date.now();
    },
    fetchIconsFailure: (state, action) => {
      state.iconsLoading = false;
      state.iconsError = action.payload;
    },

    // Subcategory icons actions
    fetchSubCategoryIconsStart: (state) => {
      state.subCategoryIconsLoading = true;
      state.subCategoryIconsError = null;
    },
    fetchSubCategoryIconsSuccess: (state, action) => {
      state.subCategoryIcons = action.payload;
      state.subCategoryIconsLoaded = true;
      state.subCategoryIconsLoading = false;
      state.subCategoryIconsError = null;
      
      // Build subcategory icon map
      const iconMap = {};
      action.payload.forEach(dto => {
        if (dto.subCategoryId && dto.base64Icon) {
          try {
            const base64 = dto.base64Icon;
            // Generate data URL for <img src="...">
            iconMap[dto.subCategoryId] = `data:image/png;base64,${base64}`;
          } catch (error) {
            console.error(`Failed to process icon for subcategory ${dto.subCategoryId}:`, error);
          }
        }
      });
      state.subCategoryIconMap = iconMap;
      state.lastUpdated = Date.now();
    },
    fetchSubCategoryIconsFailure: (state, action) => {
      state.subCategoryIconsLoading = false;
      state.subCategoryIconsError = action.payload;
    },

    // Categories actions
    fetchCategoriesStart: (state) => {
      state.categoriesLoading = true;
      state.categoriesError = null;
    },
    fetchCategoriesSuccess: (state, action) => {
      state.categories = action.payload;
      state.categoriesLoaded = true;
      state.categoriesLoading = false;
      state.categoriesError = null;
      state.lastUpdated = Date.now();
    },
    fetchCategoriesFailure: (state, action) => {
      state.categoriesLoading = false;
      state.categoriesError = action.payload;
    },

    // Subcategories actions
    fetchSubcategoriesStart: (state) => {
      state.subcategoriesLoading = true;
      state.subcategoriesError = null;
    },
    fetchSubcategoriesSuccess: (state, action) => {
      state.subcategories = action.payload;
      state.subcategoriesLoaded = true;
      state.subcategoriesLoading = false;
      state.subcategoriesError = null;
      state.lastUpdated = Date.now();
    },
    fetchSubcategoriesFailure: (state, action) => {
      state.subcategoriesLoading = false;
      state.subcategoriesError = action.payload;
    },

    // Clear cache action
    clearCache: (state) => {
      // Revoke object URLs to prevent memory leaks
      Object.values(state.subCategoryIconMap).forEach(url => {
        try {
          URL.revokeObjectURL(url);
        } catch (error) {
          console.warn('Failed to revoke object URL:', error);
        }
      });
      
      return initialState;
    },

    // Initialize cache from login
    initializeCacheStart: (state) => {
      state.iconsLoading = true;
      state.subCategoryIconsLoading = true;
      state.categoriesLoading = true;
      state.subcategoriesLoading = true;
    }
  }
});

export const {
  fetchIconsStart,
  fetchIconsSuccess,
  fetchIconsFailure,
  fetchSubCategoryIconsStart,
  fetchSubCategoryIconsSuccess,
  fetchSubCategoryIconsFailure,
  fetchCategoriesStart,
  fetchCategoriesSuccess,
  fetchCategoriesFailure,
  fetchSubcategoriesStart,
  fetchSubcategoriesSuccess,
  fetchSubcategoriesFailure,
  clearCache,
  initializeCacheStart
} = cacheSlice.actions;

export default cacheSlice.reducer;