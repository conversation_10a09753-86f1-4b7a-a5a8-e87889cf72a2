import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  items: [],
  rules: [],
  editedRules: {},
  loading: false,
  error: null,
  showNotifications: false, // New state for notification panel visibility
};

const notificationSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    // Reducers for fetching notifications
    fetchNotifications: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    fetchNotificationsSuccess: (state, action) => {
      state.loading = false;
      state.items = action.payload;
      console.log("fetchNotificationsSuccess: ", state.items);
    },
    fetchNotificationsFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    // Reducers for fetching notification rules
    fetchNotificationRules: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    fetchNotificationRulesSuccess: (state, action) => {
      state.loading = false;
      state.rules = action.payload;
      state.editedRules = action.payload.reduce((acc, rule) => {
        acc[rule.ruleId] = { ...rule };
        return acc;
      }, {});
    },
    fetchNotificationRulesFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    // Reducers for marking notifications as read
    markNotificationAsRead: (state, action) => {
      state.loading = true;
      state.error = null;
      console.log("markNotificationAsRead: ", action.payload);
    },
    markNotificationAsReadSuccess: (state, action) => {
      state.loading = false;
      const { notificationId } = action.payload;
      state.items = state.items.map((n) =>
        n.notificationId === notificationId ? { ...n, state: 'read' } : n
      );
    },
    markNotificationAsReadFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    // Reducers for clearing all notifications
    clearAllNotifications: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    clearAllNotificationsSuccess: (state) => {
      state.loading = false;
      state.items = [];
    },
    clearAllNotificationsFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    // Reducers for saving notification rules
    updateEditedRule: (state, action) => {
      const { id, field, value } = action.payload;
      if (state.editedRules[id]) {
        state.editedRules[id] = {
          ...state.editedRules[id],
          [field]: value,
        };
      }
    },
    saveNotificationRule: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    saveNotificationRuleSuccess: (state, action) => {
      const updated = action.payload;
      state.loading = false;
      state.rules = state.rules.map(rule =>
        rule.ruleId === updated.ruleId ? updated : rule
      );
      state.editedRules[updated.ruleId] = { ...updated };
    },
    saveNotificationRuleFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    // New reducer to toggle notification panel visibility
    toggleShowNotifications: (state) => {
      state.showNotifications = !state.showNotifications;
    },
     // Reducers for deleting a notification
    deleteNotification: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    deleteNotificationSuccess: (state, action) => {
      state.loading = false;
      const { notificationId } = action.payload;
      state.items = state.items.filter((n) => n.notificationId !== notificationId);
    },
    deleteNotificationFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
  },
});

export const {
  fetchNotifications,
  fetchNotificationsSuccess,
  fetchNotificationsFailure,
  fetchNotificationRules,
  fetchNotificationRulesSuccess,
  fetchNotificationRulesFailure,
  markNotificationAsRead,
  markNotificationAsReadSuccess,
  markNotificationAsReadFailure,
  clearAllNotifications,
  clearAllNotificationsSuccess,
  clearAllNotificationsFailure,
  saveNotificationRule,
  saveNotificationRuleSuccess,
  saveNotificationRuleFailure,
  updateEditedRule,
  toggleShowNotifications, // New action exported
    deleteNotification,
  deleteNotificationSuccess,
  deleteNotificationFailure,
} = notificationSlice.actions;

export default notificationSlice.reducer;