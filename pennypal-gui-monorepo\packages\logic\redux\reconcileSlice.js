import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  transactions: [],
  loading: false,
  error: null,
};

const reconcileSlice = createSlice({
  name: 'reconcile',
  initialState,
  reducers: {
    fetchReconcileRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchReconcileSuccess: (state, action) => {
      state.loading = false;
      state.transactions = action.payload;
      state.error = null;
    },
    fetchReconcileFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
      state.transactions = [];
    },
  },
});

export const {
  fetchReconcileRequest,
  fetchReconcileSuccess,
  fetchReconcileFailure,
} = reconcileSlice.actions;

export default reconcileSlice.reducer;