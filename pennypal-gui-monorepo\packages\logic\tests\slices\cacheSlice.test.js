import { describe, it, expect } from 'vitest'
import { configureStore } from '@reduxjs/toolkit'
import cacheReducer, {
  fetchIconsStart,
  fetchIconsSuccess,
  fetchIconsFailure,
  fetchSubCategoryIconsStart,
  fetchSubCategoryIconsSuccess,
  fetchSubCategoryIconsFailure,
  fetchCategoriesStart,
  fetchCategoriesSuccess,
  fetchCategoriesFailure,
  fetchSubcategoriesStart,
  fetchSubcategoriesSuccess,
  fetchSubcategoriesFailure,
  clearCache,
  initializeCacheStart
} from '../../redux/cacheSlice'

describe('Cache Slice', () => {
  describe('Initial State', () => {
    it('should return the correct initial state', () => {
      const initialState = cacheReducer(undefined, { type: 'unknown' })
      expect(initialState).toEqual({
        icons: [],
        iconsLoaded: false,
        iconsLoading: false,
        iconsError: null,
        iconMap: {},
        subCategoryIcons: [],
        subCategoryIconsLoaded: false,
        subCategoryIconsLoading: false,
        subCategoryIconsError: null,
        subCategoryIconMap: {},
        categories: [],
        categoriesLoaded: false,
        categoriesLoading: false,
        categoriesError: null,
        subcategories: [],
        subcategoriesLoaded: false,
        subcategoriesLoading: false,
        subcategoriesError: null,
        lastUpdated: null,
        cacheVersion: '1.0.0'
      })
    })
  })

  describe('Icons Actions', () => {
    it('should handle fetchIconsStart', () => {
      const previousState = {
        iconsLoading: false,
        iconsError: 'some error'
      }
      const result = cacheReducer(previousState, fetchIconsStart())
      expect(result.iconsLoading).toBe(true)
      expect(result.iconsError).toBe(null)
    })

    it('should handle fetchIconsSuccess', () => {
      const mockIcons = [
        { iconName: 'test-icon', svgContent: '<svg>test</svg>' },
        { iconName: 'another-icon', svgContent: '<svg>another</svg>' }
      ]
      const previousState = {
        iconsLoading: true,
        iconsError: null
      }
      const result = cacheReducer(previousState, fetchIconsSuccess(mockIcons))
      
      expect(result.icons).toEqual(mockIcons)
      expect(result.iconsLoaded).toBe(true)
      expect(result.iconsLoading).toBe(false)
      expect(result.iconsError).toBe(null)
      expect(result.iconMap).toEqual({
        'test-icon': '<svg>test</svg>',
        'another-icon': '<svg>another</svg>'
      })
      expect(result.lastUpdated).toBeDefined()
    })

    it('should handle fetchIconsFailure', () => {
      const errorMessage = 'Failed to fetch icons'
      const previousState = {
        iconsLoading: true
      }
      const result = cacheReducer(previousState, fetchIconsFailure(errorMessage))
      
      expect(result.iconsLoading).toBe(false)
      expect(result.iconsError).toBe(errorMessage)
    })
  })

  describe('Subcategory Icons Actions', () => {
    it('should handle fetchSubCategoryIconsSuccess', () => {
      const mockSubCategoryIcons = [
        { subCategoryId: 1, base64Icon: 'base64data1' },
        { subCategoryId: 2, base64Icon: 'base64data2' }
      ]
      const previousState = {
        subCategoryIconsLoading: true
      }
      const result = cacheReducer(previousState, fetchSubCategoryIconsSuccess(mockSubCategoryIcons))
      
      expect(result.subCategoryIcons).toEqual(mockSubCategoryIcons)
      expect(result.subCategoryIconsLoaded).toBe(true)
      expect(result.subCategoryIconsLoading).toBe(false)
      expect(result.subCategoryIconMap).toEqual({
        1: 'data:image/png;base64,base64data1',
        2: 'data:image/png;base64,base64data2'
      })
    })
  })

  describe('Categories Actions', () => {
    it('should handle fetchCategoriesSuccess', () => {
      const mockCategories = [
        { id: 1, name: 'Food' },
        { id: 2, name: 'Transport' }
      ]
      const result = cacheReducer(undefined, fetchCategoriesSuccess(mockCategories))
      
      expect(result.categories).toEqual(mockCategories)
      expect(result.categoriesLoaded).toBe(true)
      expect(result.categoriesLoading).toBe(false)
    })
  })

  describe('Subcategories Actions', () => {
    it('should handle fetchSubcategoriesSuccess', () => {
      const mockSubcategories = [
        { id: 1, name: 'Groceries', categoryId: 1 },
        { id: 2, name: 'Gas', categoryId: 2 }
      ]
      const result = cacheReducer(undefined, fetchSubcategoriesSuccess(mockSubcategories))
      
      expect(result.subcategories).toEqual(mockSubcategories)
      expect(result.subcategoriesLoaded).toBe(true)
      expect(result.subcategoriesLoading).toBe(false)
    })
  })

  describe('Cache Management', () => {
    it('should handle initializeCacheStart', () => {
      const result = cacheReducer(undefined, initializeCacheStart())
      
      expect(result.iconsLoading).toBe(true)
      expect(result.subCategoryIconsLoading).toBe(true)
      expect(result.categoriesLoading).toBe(true)
      expect(result.subcategoriesLoading).toBe(true)
    })

    it('should handle clearCache', () => {
      const stateWithData = {
        icons: [{ iconName: 'test' }],
        iconsLoaded: true,
        categories: [{ id: 1, name: 'test' }],
        categoriesLoaded: true,
        subCategoryIconMap: {},
        lastUpdated: Date.now()
      }
      const result = cacheReducer(stateWithData, clearCache())
      
      expect(result.icons).toEqual([])
      expect(result.iconsLoaded).toBe(false)
      expect(result.categories).toEqual([])
      expect(result.categoriesLoaded).toBe(false)
      expect(result.lastUpdated).toBe(null)
    })
  })
})

describe('Cache Store Integration', () => {
  let store

  beforeEach(() => {
    store = configureStore({
      reducer: {
        cache: cacheReducer
      }
    })
  })

  it('should initialize store with correct cache state', () => {
    const state = store.getState()
    expect(state.cache.iconsLoaded).toBe(false)
    expect(state.cache.categoriesLoaded).toBe(false)
    expect(state.cache.icons).toEqual([])
    expect(state.cache.categories).toEqual([])
  })

  it('should handle cache initialization workflow', () => {
    // Start cache initialization
    store.dispatch(initializeCacheStart())
    let state = store.getState()
    expect(state.cache.iconsLoading).toBe(true)
    expect(state.cache.categoriesLoading).toBe(true)

    // Complete icons loading
    const mockIcons = [{ iconName: 'test', svgContent: '<svg>test</svg>' }]
    store.dispatch(fetchIconsSuccess(mockIcons))
    state = store.getState()
    expect(state.cache.iconsLoaded).toBe(true)
    expect(state.cache.iconsLoading).toBe(false)
    expect(state.cache.icons).toEqual(mockIcons)

    // Complete categories loading
    const mockCategories = [{ id: 1, name: 'Food' }]
    store.dispatch(fetchCategoriesSuccess(mockCategories))
    state = store.getState()
    expect(state.cache.categoriesLoaded).toBe(true)
    expect(state.cache.categories).toEqual(mockCategories)
  })
})
