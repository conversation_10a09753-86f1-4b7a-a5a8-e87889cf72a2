import { useDispatch } from 'react-redux';
import {
  invalidateTransactionCache,
  invalidateRecurringTransactionCache,
  invalidateBudgetCache,
  invalidateAllTransactionRelatedCache,
  clearCache
} from '../redux/cacheSlice';

/**
 * Utility functions for manual cache invalidation
 * Use these in components when you need to manually invalidate cache
 */

/**
 * Invalidate transaction cache only
 * Use when: Individual transactions are modified but budget data is unchanged
 */
export const invalidateTransactions = (dispatch) => {
  console.log('🗑️ Manually invalidating transaction cache');
  dispatch(invalidateTransactionCache());
};

/**
 * Invalidate recurring transaction cache only
 * Use when: Recurring transactions are modified
 */
export const invalidateRecurringTransactions = (dispatch) => {
  console.log('🗑️ Manually invalidating recurring transaction cache');
  dispatch(invalidateRecurringTransactionCache());
};

/**
 * Invalidate budget cache only
 * Use when: Budget data is modified but transactions are unchanged
 */
export const invalidateBudget = (dispatch) => {
  console.log('🗑️ Manually invalidating budget cache');
  dispatch(invalidateBudgetCache());
};

/**
 * Invalidate all transaction and budget related cache
 * Use when: Major data changes that affect both transactions and budgets
 * Examples: Account sync, bulk operations, data imports
 */
export const invalidateAllTransactionData = (dispatch) => {
  console.log('🗑️ Manually invalidating all transaction-related cache');
  dispatch(invalidateAllTransactionRelatedCache());
};

/**
 * Clear entire cache (nuclear option)
 * Use when: User logs out, major app state reset, or troubleshooting
 */
export const clearAllCache = (dispatch) => {
  console.log('🗑️ Manually clearing entire cache');
  dispatch(clearCache());
};

/**
 * Hook for easy cache invalidation in React components
 * Usage: const { invalidateTransactions, invalidateBudget, ... } = useCacheInvalidation();
 */
export const useCacheInvalidation = () => {
  const dispatch = useDispatch();
  
  return {
    invalidateTransactions: () => invalidateTransactions(dispatch),
    invalidateRecurringTransactions: () => invalidateRecurringTransactions(dispatch),
    invalidateBudget: () => invalidateBudget(dispatch),
    invalidateAllTransactionData: () => invalidateAllTransactionData(dispatch),
    clearAllCache: () => clearAllCache(dispatch)
  };
};

/**
 * Cache invalidation scenarios and recommended actions:
 * 
 * 1. TRANSACTION OPERATIONS:
 *    - Add/Edit/Delete single transaction → invalidateTransactions()
 *    - Bulk transaction operations → invalidateAllTransactionData()
 *    - Hide/Unhide transactions → invalidateTransactions()
 * 
 * 2. RECURRING TRANSACTION OPERATIONS:
 *    - Add/Edit/Delete recurring transaction → invalidateRecurringTransactions()
 * 
 * 3. BUDGET OPERATIONS:
 *    - Create/Edit/Delete budget → invalidateBudget()
 *    - Bulk budget updates → invalidateBudget()
 * 
 * 4. ACCOUNT SYNC OPERATIONS:
 *    - Plaid sync → invalidateAllTransactionData()
 *    - Bank account refresh → invalidateAllTransactionData()
 *    - Import transactions → invalidateAllTransactionData()
 * 
 * 5. USER SESSION:
 *    - User logout → clearAllCache()
 *    - Switch user → clearAllCache()
 * 
 * 6. TROUBLESHOOTING:
 *    - Data inconsistency → clearAllCache()
 *    - Cache corruption → clearAllCache()
 */

// Export individual functions for direct import
export {
  invalidateTransactionCache,
  invalidateRecurringTransactionCache,
  invalidateBudgetCache,
  invalidateAllTransactionRelatedCache,
  clearCache
} from '../redux/cacheSlice';
