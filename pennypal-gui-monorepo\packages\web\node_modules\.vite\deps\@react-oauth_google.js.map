{"version": 3, "sources": ["../../../../../node_modules/@react-oauth/google/dist/index.esm.js"], "sourcesContent": ["'use client'\nimport React, { useState, useRef, useEffect, createContext, useMemo, useContext, useCallback } from 'react';\n\nfunction useLoadGsiScript(options = {}) {\r\n    const { nonce, onScriptLoadSuccess, onScriptLoadError } = options;\r\n    const [scriptLoadedSuccessfully, setScriptLoadedSuccessfully] = useState(false);\r\n    const onScriptLoadSuccessRef = useRef(onScriptLoadSuccess);\r\n    onScriptLoadSuccessRef.current = onScriptLoadSuccess;\r\n    const onScriptLoadErrorRef = useRef(onScriptLoadError);\r\n    onScriptLoadErrorRef.current = onScriptLoadError;\r\n    useEffect(() => {\r\n        const scriptTag = document.createElement('script');\r\n        scriptTag.src = 'https://accounts.google.com/gsi/client';\r\n        scriptTag.async = true;\r\n        scriptTag.defer = true;\r\n        scriptTag.nonce = nonce;\r\n        scriptTag.onload = () => {\r\n            var _a;\r\n            setScriptLoadedSuccessfully(true);\r\n            (_a = onScriptLoadSuccessRef.current) === null || _a === void 0 ? void 0 : _a.call(onScriptLoadSuccessRef);\r\n        };\r\n        scriptTag.onerror = () => {\r\n            var _a;\r\n            setScriptLoadedSuccessfully(false);\r\n            (_a = onScriptLoadErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onScriptLoadErrorRef);\r\n        };\r\n        document.body.appendChild(scriptTag);\r\n        return () => {\r\n            document.body.removeChild(scriptTag);\r\n        };\r\n    }, [nonce]);\r\n    return scriptLoadedSuccessfully;\r\n}\n\nconst GoogleOAuthContext = createContext(null);\r\nfunction GoogleOAuthProvider({ clientId, nonce, onScriptLoadSuccess, onScriptLoadError, children, }) {\r\n    const scriptLoadedSuccessfully = useLoadGsiScript({\r\n        nonce,\r\n        onScriptLoadSuccess,\r\n        onScriptLoadError,\r\n    });\r\n    const contextValue = useMemo(() => ({\r\n        clientId,\r\n        scriptLoadedSuccessfully,\r\n    }), [clientId, scriptLoadedSuccessfully]);\r\n    return (React.createElement(GoogleOAuthContext.Provider, { value: contextValue }, children));\r\n}\r\nfunction useGoogleOAuth() {\r\n    const context = useContext(GoogleOAuthContext);\r\n    if (!context) {\r\n        throw new Error('Google OAuth components must be used within GoogleOAuthProvider');\r\n    }\r\n    return context;\r\n}\n\nfunction extractClientId(credentialResponse) {\r\n    var _a;\r\n    const clientId = (_a = credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.clientId) !== null && _a !== void 0 ? _a : credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.client_id;\r\n    return clientId;\r\n}\n\nconst containerHeightMap = { large: 40, medium: 32, small: 20 };\r\nfunction GoogleLogin({ onSuccess, onError, useOneTap, promptMomentNotification, type = 'standard', theme = 'outline', size = 'large', text, shape, logo_alignment, width, locale, click_listener, containerProps, ...props }) {\r\n    const btnContainerRef = useRef(null);\r\n    const { clientId, scriptLoadedSuccessfully } = useGoogleOAuth();\r\n    const onSuccessRef = useRef(onSuccess);\r\n    onSuccessRef.current = onSuccess;\r\n    const onErrorRef = useRef(onError);\r\n    onErrorRef.current = onError;\r\n    const promptMomentNotificationRef = useRef(promptMomentNotification);\r\n    promptMomentNotificationRef.current = promptMomentNotification;\r\n    useEffect(() => {\r\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j;\r\n        if (!scriptLoadedSuccessfully)\r\n            return;\r\n        (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.initialize({\r\n            client_id: clientId,\r\n            callback: (credentialResponse) => {\r\n                var _a;\r\n                if (!(credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.credential)) {\r\n                    return (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef);\r\n                }\r\n                const { credential, select_by } = credentialResponse;\r\n                onSuccessRef.current({\r\n                    credential,\r\n                    clientId: extractClientId(credentialResponse),\r\n                    select_by,\r\n                });\r\n            },\r\n            ...props,\r\n        });\r\n        (_f = (_e = (_d = window === null || window === void 0 ? void 0 : window.google) === null || _d === void 0 ? void 0 : _d.accounts) === null || _e === void 0 ? void 0 : _e.id) === null || _f === void 0 ? void 0 : _f.renderButton(btnContainerRef.current, {\r\n            type,\r\n            theme,\r\n            size,\r\n            text,\r\n            shape,\r\n            logo_alignment,\r\n            width,\r\n            locale,\r\n            click_listener,\r\n        });\r\n        if (useOneTap)\r\n            (_j = (_h = (_g = window === null || window === void 0 ? void 0 : window.google) === null || _g === void 0 ? void 0 : _g.accounts) === null || _h === void 0 ? void 0 : _h.id) === null || _j === void 0 ? void 0 : _j.prompt(promptMomentNotificationRef.current);\r\n        return () => {\r\n            var _a, _b, _c;\r\n            if (useOneTap)\r\n                (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.cancel();\r\n        };\r\n        // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, [\r\n        clientId,\r\n        scriptLoadedSuccessfully,\r\n        useOneTap,\r\n        type,\r\n        theme,\r\n        size,\r\n        text,\r\n        shape,\r\n        logo_alignment,\r\n        width,\r\n        locale,\r\n    ]);\r\n    return (React.createElement(\"div\", { ...containerProps, ref: btnContainerRef, style: { height: containerHeightMap[size], ...containerProps === null || containerProps === void 0 ? void 0 : containerProps.style } }));\r\n}\n\nfunction googleLogout() {\r\n    var _a, _b, _c;\r\n    (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.disableAutoSelect();\r\n}\n\n/* eslint-disable import/export */\r\nfunction useGoogleLogin({ flow = 'implicit', scope = '', onSuccess, onError, onNonOAuthError, overrideScope, state, ...props }) {\r\n    const { clientId, scriptLoadedSuccessfully } = useGoogleOAuth();\r\n    const clientRef = useRef();\r\n    const onSuccessRef = useRef(onSuccess);\r\n    onSuccessRef.current = onSuccess;\r\n    const onErrorRef = useRef(onError);\r\n    onErrorRef.current = onError;\r\n    const onNonOAuthErrorRef = useRef(onNonOAuthError);\r\n    onNonOAuthErrorRef.current = onNonOAuthError;\r\n    useEffect(() => {\r\n        var _a, _b;\r\n        if (!scriptLoadedSuccessfully)\r\n            return;\r\n        const clientMethod = flow === 'implicit' ? 'initTokenClient' : 'initCodeClient';\r\n        const client = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.oauth2[clientMethod]({\r\n            client_id: clientId,\r\n            scope: overrideScope ? scope : `openid profile email ${scope}`,\r\n            callback: (response) => {\r\n                var _a, _b;\r\n                if (response.error)\r\n                    return (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef, response);\r\n                (_b = onSuccessRef.current) === null || _b === void 0 ? void 0 : _b.call(onSuccessRef, response);\r\n            },\r\n            error_callback: (nonOAuthError) => {\r\n                var _a;\r\n                (_a = onNonOAuthErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onNonOAuthErrorRef, nonOAuthError);\r\n            },\r\n            state,\r\n            ...props,\r\n        });\r\n        clientRef.current = client;\r\n        // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, [clientId, scriptLoadedSuccessfully, flow, scope, state]);\r\n    const loginImplicitFlow = useCallback((overrideConfig) => { var _a; return (_a = clientRef.current) === null || _a === void 0 ? void 0 : _a.requestAccessToken(overrideConfig); }, []);\r\n    const loginAuthCodeFlow = useCallback(() => { var _a; return (_a = clientRef.current) === null || _a === void 0 ? void 0 : _a.requestCode(); }, []);\r\n    return flow === 'implicit' ? loginImplicitFlow : loginAuthCodeFlow;\r\n}\n\nfunction useGoogleOneTapLogin({ onSuccess, onError, promptMomentNotification, cancel_on_tap_outside, prompt_parent_id, state_cookie_domain, hosted_domain, use_fedcm_for_prompt = false, use_fedcm_for_button = false, disabled, auto_select, }) {\r\n    const { clientId, scriptLoadedSuccessfully } = useGoogleOAuth();\r\n    const onSuccessRef = useRef(onSuccess);\r\n    onSuccessRef.current = onSuccess;\r\n    const onErrorRef = useRef(onError);\r\n    onErrorRef.current = onError;\r\n    const promptMomentNotificationRef = useRef(promptMomentNotification);\r\n    promptMomentNotificationRef.current = promptMomentNotification;\r\n    useEffect(() => {\r\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j;\r\n        if (!scriptLoadedSuccessfully)\r\n            return;\r\n        if (disabled) {\r\n            (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.cancel();\r\n            return;\r\n        }\r\n        (_f = (_e = (_d = window === null || window === void 0 ? void 0 : window.google) === null || _d === void 0 ? void 0 : _d.accounts) === null || _e === void 0 ? void 0 : _e.id) === null || _f === void 0 ? void 0 : _f.initialize({\r\n            client_id: clientId,\r\n            callback: (credentialResponse) => {\r\n                var _a;\r\n                if (!(credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.credential)) {\r\n                    return (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef);\r\n                }\r\n                const { credential, select_by } = credentialResponse;\r\n                onSuccessRef.current({\r\n                    credential,\r\n                    clientId: extractClientId(credentialResponse),\r\n                    select_by,\r\n                });\r\n            },\r\n            hosted_domain,\r\n            cancel_on_tap_outside,\r\n            prompt_parent_id,\r\n            state_cookie_domain,\r\n            use_fedcm_for_prompt,\r\n            use_fedcm_for_button,\r\n            auto_select,\r\n        });\r\n        (_j = (_h = (_g = window === null || window === void 0 ? void 0 : window.google) === null || _g === void 0 ? void 0 : _g.accounts) === null || _h === void 0 ? void 0 : _h.id) === null || _j === void 0 ? void 0 : _j.prompt(promptMomentNotificationRef.current);\r\n        return () => {\r\n            var _a, _b, _c;\r\n            (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.cancel();\r\n        };\r\n    }, [\r\n        clientId,\r\n        scriptLoadedSuccessfully,\r\n        cancel_on_tap_outside,\r\n        prompt_parent_id,\r\n        state_cookie_domain,\r\n        hosted_domain,\r\n        use_fedcm_for_prompt,\r\n        use_fedcm_for_button,\r\n        disabled,\r\n        auto_select,\r\n    ]);\r\n}\n\n/**\r\n * Checks if the user granted all the specified scope or scopes\r\n * @returns True if all the scopes are granted\r\n */\r\nfunction hasGrantedAllScopesGoogle(tokenResponse, firstScope, ...restScopes) {\r\n    var _a, _b, _c;\r\n    if (!(window === null || window === void 0 ? void 0 : window.google))\r\n        return false;\r\n    return (((_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.oauth2) === null || _c === void 0 ? void 0 : _c.hasGrantedAllScopes(tokenResponse, firstScope, ...restScopes)) || false);\r\n}\n\n/**\r\n * Checks if the user granted any of the specified scope or scopes.\r\n * @returns True if any of the scopes are granted\r\n */\r\nfunction hasGrantedAnyScopeGoogle(tokenResponse, firstScope, ...restScopes) {\r\n    var _a, _b, _c;\r\n    if (!(window === null || window === void 0 ? void 0 : window.google))\r\n        return false;\r\n    return (((_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.oauth2) === null || _c === void 0 ? void 0 : _c.hasGrantedAnyScope(tokenResponse, firstScope, ...restScopes)) || false);\r\n}\n\nexport { GoogleLogin, GoogleOAuthProvider, googleLogout, hasGrantedAllScopesGoogle, hasGrantedAnyScopeGoogle, useGoogleLogin, useGoogleOAuth, useGoogleOneTapLogin };\n"], "mappings": ";;;;;;;;;AACA,mBAAoG;AAEpG,SAAS,iBAAiB,UAAU,CAAC,GAAG;AACpC,QAAM,EAAE,OAAO,qBAAqB,kBAAkB,IAAI;AAC1D,QAAM,CAAC,0BAA0B,2BAA2B,QAAI,uBAAS,KAAK;AAC9E,QAAM,6BAAyB,qBAAO,mBAAmB;AACzD,yBAAuB,UAAU;AACjC,QAAM,2BAAuB,qBAAO,iBAAiB;AACrD,uBAAqB,UAAU;AAC/B,8BAAU,MAAM;AACZ,UAAM,YAAY,SAAS,cAAc,QAAQ;AACjD,cAAU,MAAM;AAChB,cAAU,QAAQ;AAClB,cAAU,QAAQ;AAClB,cAAU,QAAQ;AAClB,cAAU,SAAS,MAAM;AACrB,UAAI;AACJ,kCAA4B,IAAI;AAChC,OAAC,KAAK,uBAAuB,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,sBAAsB;AAAA,IAC7G;AACA,cAAU,UAAU,MAAM;AACtB,UAAI;AACJ,kCAA4B,KAAK;AACjC,OAAC,KAAK,qBAAqB,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,oBAAoB;AAAA,IACzG;AACA,aAAS,KAAK,YAAY,SAAS;AACnC,WAAO,MAAM;AACT,eAAS,KAAK,YAAY,SAAS;AAAA,IACvC;AAAA,EACJ,GAAG,CAAC,KAAK,CAAC;AACV,SAAO;AACX;AAEA,IAAM,yBAAqB,4BAAc,IAAI;AAC7C,SAAS,oBAAoB,EAAE,UAAU,OAAO,qBAAqB,mBAAmB,SAAU,GAAG;AACjG,QAAM,2BAA2B,iBAAiB;AAAA,IAC9C;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,QAAM,mBAAe,sBAAQ,OAAO;AAAA,IAChC;AAAA,IACA;AAAA,EACJ,IAAI,CAAC,UAAU,wBAAwB,CAAC;AACxC,SAAQ,aAAAA,QAAM,cAAc,mBAAmB,UAAU,EAAE,OAAO,aAAa,GAAG,QAAQ;AAC9F;AACA,SAAS,iBAAiB;AACtB,QAAM,cAAU,yBAAW,kBAAkB;AAC7C,MAAI,CAAC,SAAS;AACV,UAAM,IAAI,MAAM,iEAAiE;AAAA,EACrF;AACA,SAAO;AACX;AAEA,SAAS,gBAAgB,oBAAoB;AACzC,MAAI;AACJ,QAAM,YAAY,KAAK,uBAAuB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,cAAc,QAAQ,OAAO,SAAS,KAAK,uBAAuB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB;AACxP,SAAO;AACX;AAEA,IAAM,qBAAqB,EAAE,OAAO,IAAI,QAAQ,IAAI,OAAO,GAAG;AAC9D,SAAS,YAAY,EAAE,WAAW,SAAS,WAAW,0BAA0B,OAAO,YAAY,QAAQ,WAAW,OAAO,SAAS,MAAM,OAAO,gBAAgB,OAAO,QAAQ,gBAAgB,gBAAgB,GAAG,MAAM,GAAG;AAC1N,QAAM,sBAAkB,qBAAO,IAAI;AACnC,QAAM,EAAE,UAAU,yBAAyB,IAAI,eAAe;AAC9D,QAAM,mBAAe,qBAAO,SAAS;AACrC,eAAa,UAAU;AACvB,QAAM,iBAAa,qBAAO,OAAO;AACjC,aAAW,UAAU;AACrB,QAAM,kCAA8B,qBAAO,wBAAwB;AACnE,8BAA4B,UAAU;AACtC,8BAAU,MAAM;AACZ,QAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACpC,QAAI,CAAC;AACD;AACJ,KAAC,MAAM,MAAM,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAAA,MAC9N,WAAW;AAAA,MACX,UAAU,CAAC,uBAAuB;AAC9B,YAAIC;AACJ,YAAI,EAAE,uBAAuB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,aAAa;AAC1G,kBAAQA,MAAK,WAAW,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,UAAU;AAAA,QAC5F;AACA,cAAM,EAAE,YAAY,UAAU,IAAI;AAClC,qBAAa,QAAQ;AAAA,UACjB;AAAA,UACA,UAAU,gBAAgB,kBAAkB;AAAA,UAC5C;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AACD,KAAC,MAAM,MAAM,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,gBAAgB,SAAS;AAAA,MACzP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,QAAI;AACA,OAAC,MAAM,MAAM,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO,4BAA4B,OAAO;AACrQ,WAAO,MAAM;AACT,UAAIA,KAAIC,KAAIC;AACZ,UAAI;AACA,SAACA,OAAMD,OAAMD,MAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,QAAQA,QAAO,SAAS,SAASA,IAAG,cAAc,QAAQC,QAAO,SAAS,SAASA,IAAG,QAAQ,QAAQC,QAAO,SAAS,SAASA,IAAG,OAAO;AAAA,IACtO;AAAA,EAEJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,SAAQ,aAAAH,QAAM,cAAc,OAAO,EAAE,GAAG,gBAAgB,KAAK,iBAAiB,OAAO,EAAE,QAAQ,mBAAmB,IAAI,GAAG,GAAG,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,MAAM,EAAE,CAAC;AACxN;AAEA,SAAS,eAAe;AACpB,MAAI,IAAI,IAAI;AACZ,GAAC,MAAM,MAAM,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,QAAQ,OAAO,SAAS,SAAS,GAAG,kBAAkB;AAC7O;AAGA,SAAS,eAAe,EAAE,OAAO,YAAY,QAAQ,IAAI,WAAW,SAAS,iBAAiB,eAAe,OAAO,GAAG,MAAM,GAAG;AAC5H,QAAM,EAAE,UAAU,yBAAyB,IAAI,eAAe;AAC9D,QAAM,gBAAY,qBAAO;AACzB,QAAM,mBAAe,qBAAO,SAAS;AACrC,eAAa,UAAU;AACvB,QAAM,iBAAa,qBAAO,OAAO;AACjC,aAAW,UAAU;AACrB,QAAM,yBAAqB,qBAAO,eAAe;AACjD,qBAAmB,UAAU;AAC7B,8BAAU,MAAM;AACZ,QAAI,IAAI;AACR,QAAI,CAAC;AACD;AACJ,UAAM,eAAe,SAAS,aAAa,oBAAoB;AAC/D,UAAM,UAAU,MAAM,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO,YAAY,EAAE;AAAA,MACrM,WAAW;AAAA,MACX,OAAO,gBAAgB,QAAQ,wBAAwB,KAAK;AAAA,MAC5D,UAAU,CAAC,aAAa;AACpB,YAAIC,KAAIC;AACR,YAAI,SAAS;AACT,kBAAQD,MAAK,WAAW,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,YAAY,QAAQ;AACtG,SAACC,MAAK,aAAa,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,cAAc,QAAQ;AAAA,MACnG;AAAA,MACA,gBAAgB,CAAC,kBAAkB;AAC/B,YAAID;AACJ,SAACA,MAAK,mBAAmB,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,oBAAoB,aAAa;AAAA,MACpH;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AACD,cAAU,UAAU;AAAA,EAExB,GAAG,CAAC,UAAU,0BAA0B,MAAM,OAAO,KAAK,CAAC;AAC3D,QAAM,wBAAoB,0BAAY,CAAC,mBAAmB;AAAE,QAAI;AAAI,YAAQ,KAAK,UAAU,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,mBAAmB,cAAc;AAAA,EAAG,GAAG,CAAC,CAAC;AACrL,QAAM,wBAAoB,0BAAY,MAAM;AAAE,QAAI;AAAI,YAAQ,KAAK,UAAU,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY;AAAA,EAAG,GAAG,CAAC,CAAC;AAClJ,SAAO,SAAS,aAAa,oBAAoB;AACrD;AAEA,SAAS,qBAAqB,EAAE,WAAW,SAAS,0BAA0B,uBAAuB,kBAAkB,qBAAqB,eAAe,uBAAuB,OAAO,uBAAuB,OAAO,UAAU,YAAa,GAAG;AAC7O,QAAM,EAAE,UAAU,yBAAyB,IAAI,eAAe;AAC9D,QAAM,mBAAe,qBAAO,SAAS;AACrC,eAAa,UAAU;AACvB,QAAM,iBAAa,qBAAO,OAAO;AACjC,aAAW,UAAU;AACrB,QAAM,kCAA8B,qBAAO,wBAAwB;AACnE,8BAA4B,UAAU;AACtC,8BAAU,MAAM;AACZ,QAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACpC,QAAI,CAAC;AACD;AACJ,QAAI,UAAU;AACV,OAAC,MAAM,MAAM,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAC9N;AAAA,IACJ;AACA,KAAC,MAAM,MAAM,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAAA,MAC9N,WAAW;AAAA,MACX,UAAU,CAAC,uBAAuB;AAC9B,YAAIA;AACJ,YAAI,EAAE,uBAAuB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,aAAa;AAC1G,kBAAQA,MAAK,WAAW,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,UAAU;AAAA,QAC5F;AACA,cAAM,EAAE,YAAY,UAAU,IAAI;AAClC,qBAAa,QAAQ;AAAA,UACjB;AAAA,UACA,UAAU,gBAAgB,kBAAkB;AAAA,UAC5C;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,KAAC,MAAM,MAAM,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO,4BAA4B,OAAO;AACjQ,WAAO,MAAM;AACT,UAAIA,KAAIC,KAAIC;AACZ,OAACA,OAAMD,OAAMD,MAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,QAAQA,QAAO,SAAS,SAASA,IAAG,cAAc,QAAQC,QAAO,SAAS,SAASA,IAAG,QAAQ,QAAQC,QAAO,SAAS,SAASA,IAAG,OAAO;AAAA,IAClO;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AAMA,SAAS,0BAA0B,eAAe,eAAe,YAAY;AACzE,MAAI,IAAI,IAAI;AACZ,MAAI,EAAE,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AACzD,WAAO;AACX,WAAU,MAAM,MAAM,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,oBAAoB,eAAe,YAAY,GAAG,UAAU,MAAM;AAC1S;AAMA,SAAS,yBAAyB,eAAe,eAAe,YAAY;AACxE,MAAI,IAAI,IAAI;AACZ,MAAI,EAAE,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AACzD,WAAO;AACX,WAAU,MAAM,MAAM,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,mBAAmB,eAAe,YAAY,GAAG,UAAU,MAAM;AACzS;", "names": ["React", "_a", "_b", "_c"]}