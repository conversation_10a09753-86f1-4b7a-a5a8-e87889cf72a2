{"version": 3, "sources": ["../../../../../node_modules/@mui/icons-material/esm/AccountBalance.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M4 10h3v7H4zm6.5 0h3v7h-3zM2 19h20v3H2zm15-9h3v7h-3zm-5-9L2 6v2h20V6z\"\n}), 'AccountBalance');"], "mappings": ";;;;;;;;;;;AAGA,yBAA4B;AAC5B,IAAO,yBAAQ,kBAA2B,mBAAAA,KAAK,QAAQ;AAAA,EACrD,GAAG;AACL,CAAC,GAAG,gBAAgB;", "names": ["_jsx"]}