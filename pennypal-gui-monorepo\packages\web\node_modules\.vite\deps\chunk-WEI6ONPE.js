import {
  createSvgIcon
} from "./chunk-ABX5ABCG.js";
import {
  require_jsx_runtime
} from "./chunk-OT5EQO2H.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// ../../node_modules/@mui/icons-material/esm/Notes.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var Notes_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M3 18h12v-2H3zM3 6v2h18V6zm0 7h18v-2H3z"
}), "Notes");

export {
  Notes_default
};
//# sourceMappingURL=chunk-WEI6ONPE.js.map
