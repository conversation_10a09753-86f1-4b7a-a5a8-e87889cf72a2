import {
  merge,
  queueScheduler
} from "./chunk-WUCU5UW3.js";
import "./chunk-6EJIPLB2.js";
import {
  Observable,
  Subject,
  filter,
  from,
  map,
  mergeMap,
  observeOn,
  subscribeOn
} from "./chunk-SXWA7TM6.js";
import {
  isAction
} from "./chunk-5MV5S5BR.js";
import {
  __publicField
} from "./chunk-EWTE5DHJ.js";

// ../../node_modules/redux-observable/dist/redux-observable.mjs
var StateObservable = class extends Observable {
  constructor(input$, initialState) {
    super((subscriber) => {
      const subscription = this.__notifier.subscribe(subscriber);
      if (subscription && !subscription.closed) {
        subscriber.next(this.value);
      }
      return subscription;
    });
    __publicField(this, "value");
    __publicField(this, "__notifier", new Subject());
    this.value = initialState;
    input$.subscribe((value) => {
      if (value !== this.value) {
        this.value = value;
        this.__notifier.next(value);
      }
    });
  }
};
function combineEpics(...epics) {
  const merger = (...args) => merge(
    ...epics.map((epic) => {
      const output$ = epic(...args);
      if (!output$) {
        throw new TypeError(
          `combineEpics: one of the provided Epics "${epic.name || "<anonymous>"}" does not return a stream. Double check you're not missing a return statement!`
        );
      }
      return output$;
    })
  );
  try {
    Object.defineProperty(merger, "name", {
      value: `combineEpics(${epics.map((epic) => epic.name || "<anonymous>").join(", ")})`
    });
  } catch (e) {
  }
  return merger;
}
var deprecationsSeen = {};
var resetDeprecationsSeen = () => {
  deprecationsSeen = {};
};
var consoleWarn = typeof console === "object" && typeof console.warn === "function" ? console.warn.bind(console) : () => {
};
var warn = (msg) => {
  consoleWarn(`redux-observable | WARNING: ${msg}`);
};
function createEpicMiddleware(options = {}) {
  const QueueScheduler = queueScheduler.constructor;
  const uniqueQueueScheduler = new QueueScheduler(
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    queueScheduler.schedulerActionCtor
  );
  if (typeof options === "function") {
    throw new TypeError(
      "Providing your root Epic to `createEpicMiddleware(rootEpic)` is no longer supported, instead use `epicMiddleware.run(rootEpic)`\n\nLearn more: https://redux-observable.js.org/MIGRATION.html#setting-up-the-middleware"
    );
  }
  const epic$ = new Subject();
  let store;
  const epicMiddleware = (_store) => {
    if (store) {
      warn(
        "this middleware is already associated with a store. createEpicMiddleware should be called for every store.\n\nLearn more: https://goo.gl/2GQ7Da"
      );
    }
    store = _store;
    const actionSubject$ = new Subject();
    const stateSubject$ = new Subject();
    const action$ = actionSubject$.asObservable().pipe(observeOn(uniqueQueueScheduler));
    const state$ = new StateObservable(
      stateSubject$.pipe(observeOn(uniqueQueueScheduler)),
      store.getState()
    );
    const result$ = epic$.pipe(
      map((epic) => {
        const output$ = epic(action$, state$, options.dependencies);
        if (!output$) {
          throw new TypeError(
            `Your root Epic "${epic.name || "<anonymous>"}" does not return a stream. Double check you're not missing a return statement!`
          );
        }
        return output$;
      }),
      mergeMap(
        (output$) => from(output$).pipe(
          subscribeOn(uniqueQueueScheduler),
          observeOn(uniqueQueueScheduler)
        )
      )
    );
    result$.subscribe(store.dispatch);
    return (next) => {
      return (action) => {
        const result = next(action);
        stateSubject$.next(store.getState());
        actionSubject$.next(action);
        return result;
      };
    };
  };
  epicMiddleware.run = (rootEpic) => {
    if (!store) {
      warn(
        "epicMiddleware.run(rootEpic) called before the middleware has been setup by redux. Provide the epicMiddleware instance to createStore() first."
      );
    }
    epic$.next(rootEpic);
  };
  return epicMiddleware;
}
function ofType(...types) {
  const len = types.length;
  if (true) {
    if (len === 0) {
      warn("ofType was called without any types!");
    }
    if (types.some((key) => key === null || key === void 0)) {
      warn("ofType was called with one or more undefined or null values!");
    }
  }
  return filter(
    len === 1 ? (action) => isAction(action) && action.type === types[0] : (action) => {
      if (isAction(action)) {
        for (let i = 0; i < len; i++) {
          if (action.type === types[i]) {
            return true;
          }
        }
      }
      return false;
    }
  );
}
export {
  StateObservable,
  resetDeprecationsSeen as __FOR_TESTING__resetDeprecationsSeen,
  combineEpics,
  createEpicMiddleware,
  ofType
};
//# sourceMappingURL=redux-observable.js.map
