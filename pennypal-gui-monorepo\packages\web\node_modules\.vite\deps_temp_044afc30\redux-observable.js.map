{"version": 3, "sources": ["../../../../../node_modules/redux-observable/src/StateObservable.ts", "../../../../../node_modules/redux-observable/src/combineEpics.ts", "../../../../../node_modules/redux-observable/src/createEpicMiddleware.ts", "../../../../../node_modules/redux-observable/src/utils/console.ts", "../../../../../node_modules/redux-observable/src/operators.ts"], "sourcesContent": ["import { Observable, Subject } from 'rxjs';\n\nexport class StateObservable<S> extends Observable<S> {\n  value: S;\n  private __notifier = new Subject<S>();\n\n  constructor(input$: Observable<S>, initialState: S) {\n    super((subscriber) => {\n      const subscription = this.__notifier.subscribe(subscriber);\n      if (subscription && !subscription.closed) {\n        subscriber.next(this.value);\n      }\n      return subscription;\n    });\n\n    this.value = initialState;\n    input$.subscribe((value) => {\n      // We only want to update state$ if it has actually changed since\n      // redux requires reducers use immutability patterns.\n      // This is basically what distinctUntilChanged() does but it's so simple\n      // we don't need to pull that code in\n      if (value !== this.value) {\n        this.value = value;\n        this.__notifier.next(value);\n      }\n    });\n  }\n}\n", "import { merge } from 'rxjs';\nimport type { Epic } from './epic';\n\n/**\n  Merges all epics into a single one.\n */\nexport function combineEpics<\n  Input = unknown,\n  Output extends Input = Input,\n  State = void,\n  Dependencies = any\n>(\n  ...epics: Epic<Input, Output, State, Dependencies>[]\n): Epic<Input, Output, State, Dependencies> {\n  const merger: Epic<Input, Output, State, Dependencies> = (...args) =>\n    merge(\n      ...epics.map((epic) => {\n        const output$ = epic(...args);\n        if (!output$) {\n          throw new TypeError(\n            `combineEpics: one of the provided Epics \"${\n              epic.name || '<anonymous>'\n            }\" does not return a stream. Double check you're not missing a return statement!`\n          );\n        }\n        return output$;\n      })\n    );\n\n  // Technically the `name` property on Function's are supposed to be read-only.\n  // While some JS runtimes allow it anyway (so this is useful in debugging)\n  // some actually throw an exception when you attempt to do so.\n  try {\n    Object.defineProperty(merger, 'name', {\n      value: `combineEpics(${epics\n        .map((epic) => epic.name || '<anonymous>')\n        .join(', ')})`,\n    });\n  } catch (e) {\n    // noop\n  }\n\n  return merger;\n}\n", "import type { Dispatch, Middleware, MiddlewareAPI } from 'redux';\nimport { Subject, from, queueScheduler } from 'rxjs';\nimport { map, mergeMap, observeOn, subscribeOn } from 'rxjs/operators';\nimport { StateObservable } from './StateObservable';\nimport type { Epic } from './epic';\nimport { warn } from './utils/console';\n\ninterface Options<D = any> {\n  dependencies?: D;\n}\n\nexport interface EpicMiddleware<\n  Input = unknown,\n  Output extends Input = Input,\n  State = void,\n  Dependencies = any\n  // eslint-disable-next-line @typescript-eslint/ban-types\n> extends Middleware<{}, State> {\n  run(rootEpic: Epic<Input, Output, State, Dependencies>): void;\n}\n\nexport function createEpicMiddleware<\n  Input = unknown,\n  Output extends Input = Input,\n  State = void,\n  Dependencies = any\n>(\n  options: Options<Dependencies> = {}\n): EpicMiddleware<Input, Output, State, Dependencies> {\n  // This isn't great. RxJS doesn't publicly export the constructor for\n  // QueueScheduler nor QueueAction, so we reach in. We need to do this because\n  // we don't want our internal queuing mechanism to be on the same queue as any\n  // other RxJS code outside of redux-observable internals.\n  const QueueScheduler: any = queueScheduler.constructor;\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call\n  const uniqueQueueScheduler: typeof queueScheduler = new QueueScheduler(\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    (queueScheduler as any).schedulerActionCtor\n  );\n\n  if (process.env.NODE_ENV !== 'production' && typeof options === 'function') {\n    throw new TypeError(\n      'Providing your root Epic to `createEpicMiddleware(rootEpic)` is no longer supported, instead use `epicMiddleware.run(rootEpic)`\\n\\nLearn more: https://redux-observable.js.org/MIGRATION.html#setting-up-the-middleware'\n    );\n  }\n\n  const epic$ = new Subject<Epic<Input, Output, State, Dependencies>>();\n  let store: MiddlewareAPI<Dispatch<any>, State>;\n\n  const epicMiddleware: EpicMiddleware<Input, Output, State, Dependencies> = (\n    _store\n  ) => {\n    if (process.env.NODE_ENV !== 'production' && store) {\n      // https://github.com/redux-observable/redux-observable/issues/389\n      warn(\n        'this middleware is already associated with a store. createEpicMiddleware should be called for every store.\\n\\nLearn more: https://goo.gl/2GQ7Da'\n      );\n    }\n    store = _store;\n    const actionSubject$ = new Subject<Input>();\n    const stateSubject$ = new Subject<State>();\n    const action$ = actionSubject$\n      .asObservable()\n      .pipe(observeOn(uniqueQueueScheduler));\n    const state$ = new StateObservable(\n      stateSubject$.pipe(observeOn(uniqueQueueScheduler)),\n      store.getState()\n    );\n\n    const result$ = epic$.pipe(\n      map((epic) => {\n        const output$ = epic(action$, state$, options.dependencies!);\n\n        if (!output$) {\n          throw new TypeError(\n            `Your root Epic \"${\n              epic.name || '<anonymous>'\n            }\" does not return a stream. Double check you're not missing a return statement!`\n          );\n        }\n\n        return output$;\n      }),\n      mergeMap((output$) =>\n        from(output$).pipe(\n          subscribeOn(uniqueQueueScheduler),\n          observeOn(uniqueQueueScheduler)\n        )\n      )\n    );\n\n    result$.subscribe(store.dispatch);\n\n    return (next) => {\n      return (action) => {\n        // Downstream middleware gets the action first,\n        // which includes their reducers, so state is\n        // updated before epics receive the action\n        const result = next(action);\n\n        // It's important to update the state$ before we emit\n        // the action because otherwise it would be stale\n        stateSubject$.next(store.getState());\n        actionSubject$.next(action as Input);\n\n        return result;\n      };\n    };\n  };\n\n  epicMiddleware.run = (rootEpic) => {\n    if (process.env.NODE_ENV !== 'production' && !store) {\n      warn(\n        'epicMiddleware.run(rootEpic) called before the middleware has been setup by redux. Provide the epicMiddleware instance to createStore() first.'\n      );\n    }\n    epic$.next(rootEpic);\n  };\n\n  return epicMiddleware;\n}\n", "let deprecationsSeen: { [key: string]: boolean } = {};\nexport const resetDeprecationsSeen = (): void => {\n  deprecationsSeen = {};\n};\n\nconst consoleWarn =\n  typeof console === 'object' && typeof console.warn === 'function'\n    ? console.warn.bind(console)\n    : () => {};\n\nexport const deprecate = (msg: string): void => {\n  if (!deprecationsSeen[msg]) {\n    deprecationsSeen[msg] = true;\n    consoleWarn(`redux-observable | DEPRECATION: ${msg}`);\n  }\n};\n\nexport const warn = (msg: string): void => {\n  consoleWarn(`redux-observable | WARNING: ${msg}`);\n};\n", "import { isAction, type Action } from 'redux';\nimport type { OperatorFunction } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport { warn } from './utils/console';\n\n/**\n * Inferring the types of this is a bit challenging, and only works in newer\n * versions of TypeScript.\n *\n * @param ...types One or more Redux action types you want to filter for, variadic.\n */\nexport function ofType<\n  // All possible actions your app can dispatch\n  Input,\n  // The types you want to filter for\n  Type extends string,\n  // The resulting actions that match the above types\n  Output extends Input = Extract<Input, Action<Type>>\n>(...types: [Type, ...Type[]]): OperatorFunction<Input, Output> {\n  const len = types.length;\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (len === 0) {\n      warn('ofType was called without any types!');\n    }\n    if (types.some((key) => key === null || key === undefined)) {\n      warn('ofType was called with one or more undefined or null values!');\n    }\n  }\n\n  return filter(\n    len === 1\n      ? (action): action is Output => isAction(action) && action.type === types[0]\n      : (action): action is Output => {\n        if (isAction(action)) {\n          for (let i = 0; i < len; i++) {\n            if (action.type === types[i]) {\n              return true;\n            }\n          }\n        }\n\n        return false;\n      }\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAEO,IAAM,kBAAN,cAAiC,WAAc;EAIpD,YAAY,QAAuB,cAAiB;AAClD,UAAM,CAAC,eAAe;AACpB,YAAM,eAAe,KAAK,WAAW,UAAU,UAAU;AACzD,UAAI,gBAAgB,CAAC,aAAa,QAAQ;AACxC,mBAAW,KAAK,KAAK,KAAK;MAC5B;AACA,aAAO;IACT,CAAC;AAVH;AACQ,sCAAa,IAAI,QAAW;AAWlC,SAAK,QAAQ;AACb,WAAO,UAAU,CAAC,UAAU;AAK1B,UAAI,UAAU,KAAK,OAAO;AACxB,aAAK,QAAQ;AACb,aAAK,WAAW,KAAK,KAAK;MAC5B;IACF,CAAC;EACH;AACF;ACrBO,SAAS,gBAMX,OACuC;AAC1C,QAAM,SAAmD,IAAI,SAC3D;IACE,GAAG,MAAM,IAAI,CAAC,SAAS;AACrB,YAAM,UAAU,KAAK,GAAG,IAAI;AAC5B,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI;UACR,4CACE,KAAK,QAAQ,aACf;QACF;MACF;AACA,aAAO;IACT,CAAC;EACH;AAKF,MAAI;AACF,WAAO,eAAe,QAAQ,QAAQ;MACpC,OAAO,gBAAgB,MACpB,IAAI,CAAC,SAAS,KAAK,QAAQ,aAAa,EACxC,KAAK,IAAI,CAAC;IACf,CAAC;EACH,SAAS,GAAG;EAEZ;AAEA,SAAO;AACT;AE3CA,IAAI,mBAA+C,CAAC;AAC7C,IAAM,wBAAwB,MAAY;AAC/C,qBAAmB,CAAC;AACtB;AAEA,IAAM,cACJ,OAAO,YAAY,YAAY,OAAO,QAAQ,SAAS,aACnD,QAAQ,KAAK,KAAK,OAAO,IACzB,MAAM;AAAC;AASN,IAAM,OAAO,CAAC,QAAsB;AACzC,cAAY,+BAA+B,GAAG,EAAE;AAClD;ADEO,SAAS,qBAMd,UAAiC,CAAC,GACkB;AAKpD,QAAM,iBAAsB,eAAe;AAE3C,QAAM,uBAA8C,IAAI;;IAErD,eAAuB;EAC1B;AAEA,MAA6C,OAAO,YAAY,YAAY;AAC1E,UAAM,IAAI;MACR;IACF;EACF;AAEA,QAAM,QAAQ,IAAIA,QAAkD;AACpE,MAAI;AAEJ,QAAM,iBAAqE,CACzE,WACG;AACH,QAA6C,OAAO;AAElD;QACE;MACF;IACF;AACA,YAAQ;AACR,UAAM,iBAAiB,IAAIA,QAAe;AAC1C,UAAM,gBAAgB,IAAIA,QAAe;AACzC,UAAM,UAAU,eACb,aAAa,EACb,KAAK,UAAU,oBAAoB,CAAC;AACvC,UAAM,SAAS,IAAI;MACjB,cAAc,KAAK,UAAU,oBAAoB,CAAC;MAClD,MAAM,SAAS;IACjB;AAEA,UAAM,UAAU,MAAM;MACpB,IAAI,CAAC,SAAS;AACZ,cAAM,UAAU,KAAK,SAAS,QAAQ,QAAQ,YAAa;AAE3D,YAAI,CAAC,SAAS;AACZ,gBAAM,IAAI;YACR,mBACE,KAAK,QAAQ,aACf;UACF;QACF;AAEA,eAAO;MACT,CAAC;MACD;QAAS,CAAC,YACR,KAAK,OAAO,EAAE;UACZ,YAAY,oBAAoB;UAChC,UAAU,oBAAoB;QAChC;MACF;IACF;AAEA,YAAQ,UAAU,MAAM,QAAQ;AAEhC,WAAO,CAAC,SAAS;AACf,aAAO,CAAC,WAAW;AAIjB,cAAM,SAAS,KAAK,MAAM;AAI1B,sBAAc,KAAK,MAAM,SAAS,CAAC;AACnC,uBAAe,KAAK,MAAe;AAEnC,eAAO;MACT;IACF;EACF;AAEA,iBAAe,MAAM,CAAC,aAAa;AACjC,QAA6C,CAAC,OAAO;AACnD;QACE;MACF;IACF;AACA,UAAM,KAAK,QAAQ;EACrB;AAEA,SAAO;AACT;AE7GO,SAAS,UAOX,OAA2D;AAC9D,QAAM,MAAM,MAAM;AAElB,MAAI,MAAuC;AACzC,QAAI,QAAQ,GAAG;AACb,WAAK,sCAAsC;IAC7C;AACA,QAAI,MAAM,KAAK,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAS,GAAG;AAC1D,WAAK,8DAA8D;IACrE;EACF;AAEA,SAAO;IACL,QAAQ,IACJ,CAAC,WAA6B,SAAS,MAAM,KAAK,OAAO,SAAS,MAAM,CAAC,IACzE,CAAC,WAA6B;AAC9B,UAAI,SAAS,MAAM,GAAG;AACpB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,OAAO,SAAS,MAAM,CAAC,GAAG;AAC5B,mBAAO;UACT;QACF;MACF;AAEA,aAAO;IACT;EACJ;AACF;", "names": ["Subject"]}