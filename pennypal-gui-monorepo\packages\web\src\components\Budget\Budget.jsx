import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { setBudgetData, changeMonth, addBudget } from '../../../../logic/redux/budgetSlice';
import { axiosInstance } from '../../../../logic/api/axiosConfig';
import Cookies from 'js-cookie';

import {
  FaWallet,
  FaDollarSign,
  FaCreditCard,
  FaBuilding,
  FaChevronDown,
  FaChevronUp,
  FaArrowCircleRight,
  FaArrowCircleLeft,
  FaPlus,
  FaMinus,
  FaQuestionCircle,
  FaTimes
} from "react-icons/fa";
import * as FaIcons from "react-icons/fa";
import { MdPhone, MdLocalCafe, MdFlightTakeoff, MdMedicalServices } from "react-icons/md";
import axios from "axios";
import "./Budget.css";

const iconMapping = {
  FaMoneyBillWave: FaIcons.FaMoneyBillWave,
  FaCar: FaIcons.FaCar,
  FaHome: FaIcons.FaHome,
  FaUtensils: FaIcons.FaUtensils,
  FaPlane: FaIcons.FaPlane,
  FaShoppingCart: FaIcons.FaShoppingCart,
  FaChild: FaIcons.FaChild,
  FaHeart: FaIcons.FaHeart,
  FaBusinessTime: FaIcons.FaBusinessTime,
  FaHospital: FaIcons.FaHospital,
  FaGraduationCap: FaIcons.FaGraduationCap,
  FaFilm: FaIcons.FaFilm,
  FaMiscellaneous: FaQuestionCircle, // fallback
  MdPhone: MdPhone,
  MdLocalCafe: MdLocalCafe,
  MdFlightTakeoff: MdFlightTakeoff,
  MdMedicalServices: MdMedicalServices,
};

const customSubcategoryIconMapping = {
  "Paychecks": "FaMoneyBillWave",
  "Interest": "FaMoneyBillWave",
  "Other Income": "FaMoneyBillWave",
  "Business income": "FaHome",
  "Auto Payment": "FaCar",
  "Auto Maintenance": "FaCar",
  "Parking and Tolls": "FaCar",
  "Taxi and Ride Shares": "FaCar",
  "Public Transit": "FaCar",
  "Gas/Petrol": "FaCar",
  "Insurance": "FaMoneyBillWave",
  "Mortgage": "FaHome",
  "Home Improvement": "FaHome",
  "HOA": "FaHome",
  "Rent": "FaHome",
  "Garbage": "FaHome",
  "Water": "FaHome",
  "Electric": "FaHome",
  "Gas": "FaHome",
  "Internet": "MdPhone",
  "Cable": "MdPhone",
  "Phone": "MdPhone",
  "Subscription": "MdPhone",
  "Groceries": "FaShoppingCart",
  "Restaurants/Bars": "FaUtensils",
  "Coffee Shop": "MdLocalCafe",
  "Travel and Vacation": "MdFlightTakeoff",
  "Entertainment and Recreation": "MdFlightTakeoff",
  "Personal": "FaHome",
  "Pets": "FaHome",
  "Pocket Money": "FaMoneyBillWave",
  "Clothing": "FaHome",
  "Electronics": "FaHome",
  "Shopping": "FaShoppingCart",
  "Furniture": "FaHome",
  "Education": "MdMedicalServices",
  "Child Care": "MdMedicalServices",
  "Private Classes": "MdMedicalServices",
  "Student Loans": "MdMedicalServices",
  "Medical": "MdMedicalServices",
  "Dentist": "MdMedicalServices",
  "Advertising/Promotion": "FaHome",
  "Business Utilities and Communication": "FaHome",
  "Employee Wages and Contract": "FaHome",
  "Business Travel and Meals": "MdFlightTakeoff",
  "Business Auto Expense": "FaCar",
  "Business Insurance": "FaMoneyBillWave",
  "Office Supplies and Expenses": "FaHome",
  "Office Rent": "FaHome",
  "Shipping": "FaHome",
  "Taxes": "FaHome",
  "Loan Repayment": "FaHome",
  "Financial and Legal Services": "FaMoneyBillWave",
  "Financial Fees": "FaMoneyBillWave",
  "Cash and ATM": "FaMoneyBillWave",
  "Check": "FaMoneyBillWave",
  "eee": "FaHeart"
};

function removeDuplicateSubcategoryNames(data) {
  return data.map((budgetDto) => {
    const subs = budgetDto.category?.subCategories || [];
    const seen = new Set();
    const uniqueSubs = [];
    for (const sub of subs) {
      const name = (sub.subCategory || sub.customSubCategory || "").trim().toLowerCase();
      if (!seen.has(name)) {
        seen.add(name);
        uniqueSubs.push(sub);
      }
    }
    return {
      ...budgetDto,
      category: {
        ...budgetDto.category,
        subCategories: uniqueSubs,
      },
    };
  });
}

const Budget = () => {
  const dispatch = useDispatch();
  const { currentMonth, currentYear, budgetData } = useSelector((state) => state.budget);

  const [loading, setLoading] = useState(true);
  const [showAllTables, setShowAllTables] = useState(true);
  const [expandedCategories, setExpandedCategories] = useState({});
  const [showZeroBudget, setShowZeroBudget] = useState({});

  // State for the popup form
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  // For inline editing: we track the compositeKey of the row being edited and the new value.
  const [editingSubcategory, setEditingSubcategory] = useState(null);
  const [editedBudget, setEditedBudget] = useState({ key: null, value: 0 });




  // Convert month index to name
  const monthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

   const handleToggleAllTables = () => {
    setShowAllTables(!showAllTables);
  };

  const handleToggleCategory = (categoryId) => {
    setExpandedCategories((prevState) => ({
      ...prevState,
      [categoryId]: !prevState[categoryId],
    }));
  };

  const handleToggleZeroBudget = (categoryId) => {
    setShowZeroBudget((prevState) => ({
      ...prevState,
      [categoryId]: !prevState[categoryId],
    }));
  };
  useEffect(() => {
    const fetchBudgetData = async () => {
      try {
        // Debug check for token
        const token = Cookies.get('jwt_token');
        console.log('Current token:', token);
        
        const response = await axiosInstance.get(
          `/pennypal/api/v1/budget/user/1/month/${currentMonth + 1}`
        );
        console.log("Fetched Data for Month:", currentMonth + 1);
        const noDupSubs = removeDuplicateSubcategoryNames(response.data);
        const updatedData = noDupSubs.map((budgetDto) => {
          if (budgetDto.category && Array.isArray(budgetDto.category.subCategories)) {
            budgetDto.category.subCategories = budgetDto.category.subCategories.map(
              (subCategory, index) => {
                if (!subCategory.subCategory) {
                  subCategory.subCategory = subCategory.customSubCategory;
                }
                if (!subCategory.iconKey) {
                  subCategory.iconKey =
                    customSubcategoryIconMapping[subCategory.subCategory] || "FaMiscellaneous";
                }
                subCategory.icon_key = subCategory.iconKey;
                // Create a compositeKey using the subcategory id and index
                subCategory.compositeKey = `${subCategory.id}-${index}`;
                // For custom subcategories, if id is 0, fall back to the parent budget's id
                if ((!subCategory.id || subCategory.id === 0) && budgetDto.id) {
                  subCategory.id = budgetDto.id;
                }
                return subCategory;
              }
            );
          }
          if (budgetDto.category && !budgetDto.category.categoryIconKey) {
            budgetDto.category.categoryIconKey = "FaMiscellaneous";
          }
          return budgetDto;
        });

        dispatch(setBudgetData(updatedData)); // Update Redux store with new data
        setLoading(false);
      } catch (error) {
        console.error('Error fetching budget data:', error);
        console.error('Request config:', error.config);
        setLoading(false);
      }
    };

    fetchBudgetData();
  }, [dispatch, currentMonth,currentYear]); // Re-fetch data whenever the month changes

  if (loading) return <p>Loading budget data...</p>;

  // Calculate total actual for Category 1
  const calculateCategory1Actual = () => {
    if (budgetData.length === 0) return 0;

    const category1 = budgetData[0]; // Get Category 1
    let totalActual = category1.category?.actual || 0;

    // Add subcategory actual (spent) values
    category1.category?.subCategories?.forEach((subcategory) => {
      totalActual += subcategory.spent || 0;
    });

    return totalActual;
  };

  const category1Actual = calculateCategory1Actual(); // Get actual total for Category 1

  // Calculate other totals for summary cards (Income, Budget, Actual, Remaining)
  const calculateTotals = () => {
    let totalBudget = 0;
    let totalActual = 0;
    let totalRemaining = 0;

    budgetData.forEach((category) => {
      totalBudget += category.category?.allocated || 0;
      totalActual += category.category?.actual || 0;
      totalRemaining += category.category?.remaining || 0;

      // Subcategories totals
      category.category?.subCategories?.forEach((subcategory) => {
        totalBudget += subcategory.budget || 0;
        totalActual += subcategory.spent || 0;  // Add subcategory spent to actual value
        totalRemaining += subcategory.remaining || 0;
      });
    });
    return { totalBudget, totalActual, totalRemaining };
  };
  const { totalBudget, totalActual, totalRemaining } = calculateTotals();

  // Inline editing functions using the compositeKey.
  const handleBudgetDoubleClick = (subcategory) => {
    setEditedBudget({ key: subcategory.compositeKey, value: subcategory.budget || 0 });
    setEditingSubcategory(subcategory.compositeKey);
  };

  const handleBudgetChange = (e) => {
    setEditedBudget((prev) => ({
      ...prev,
      value: Number(e.target.innerText) || 0, // Ensure it's a valid number
    }));
  };

const saveBudgetChange = async (categoryId, subcategoryId) => {
  setEditingSubcategory(null); // Exit edit mode

  // Find the updated subcategory in Redux state
  const category = budgetData.find((cat) => cat.id === categoryId);
  if (!category) {
    console.error("Category not found!");
    return;
  }

  const subcategory = category.category.subCategories.find((sub) => sub.id === subcategoryId);
  if (!subcategory) {
    console.error("Subcategory not found!");
    return;
  }

  const newAllocatedValue = Number(editedBudget.value);
  if (isNaN(newAllocatedValue)) {
    console.error("Invalid budget value");
    return;
  }

    // Determine a valid budget ID (fall back to sub_category_id if necessary)
    let budgetId = subcategory.budgetId || subcategory.id;
    if (!budgetId || budgetId === 0) {
      budgetId = subcategory.sub_category_id;
    }
    if (!budgetId || budgetId === 0) {
      console.error("Invalid budget ID:", budgetId);
      return;
    }
const formattedDate = new Date(currentYear, currentMonth, 1).toLocaleDateString("en-CA");

  console.log("Sending update request with:", {
    budgetId,
    allocated: newAllocatedValue,
    actual: subcategory.spent || 0,
    isDynamic: subcategory.isDynamic || false,
    dynamicAllocated: subcategory.dynamicAllocated || 0,
    date: formattedDate,
  });

  // Update Redux state immediately for smoother UI
  const updatedBudgetData = budgetData.map((cat) =>
    cat.id === categoryId
      ? {
          ...cat,
          category: {
            ...cat.category,
            subCategories: cat.category.subCategories.map((sub) =>
              sub.id === subcategoryId
                ? {
                    ...sub,
                    budget: newAllocatedValue,
                    remaining: newAllocatedValue - (subcategory.spent || 0),
                    date: formattedDate,
                  }
                : sub
            ),
          },
        }
      : cat
  );

  dispatch(setBudgetData(updatedBudgetData));

    // In the payload, preserve the custom subcategory name by checking both fields.
    const payloadCustomName = subcategory.customSubCategory || subcategory.subCategory;
  
    try {
      const response = await axiosInstance.put(`/pennypal/api/v1/budget/${budgetId}`, {
        id: budgetId,
        allocated: newAllocatedValue,
        actual: subcategory.spent || 0,
        isDynamic: subcategory.isDynamic || false,
        dynamicAllocated: subcategory.dynamicAllocated || 0,
        date: formattedDate,
        customSubCategory: payloadCustomName
      });

      if (response.status !== 200) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      console.log("Budget updated successfully.");
    } catch (error) {
      console.error("Error updating budget:", error);
    }
  };
  
  
  const handlePopupToggle = () => {
    setIsPopupOpen(!isPopupOpen);
  };

  const handleSaveBudget = (budgetItem) => {
    console.log("Budget item saved:", budgetItem);
    setIsPopupOpen(false);
  };

  return (
    <div className="budget-container">
      {/* Header Section */}
      <div className="budget-header">
        <h1>Budget</h1>
        <div className="calendar-navigation">
          <span className="calendar-arrow" onClick={() => dispatch(changeMonth(-1))}>
            <FaArrowCircleLeft />
          </span>
          <span>{monthNames[currentMonth]} {currentYear}</span>
          <span className="calendar-arrow" onClick={() => dispatch(changeMonth(1))}>
            <FaArrowCircleRight />
          </span>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="summary-cards">
        <div className="card">
          <strong>Income<FaWallet className="icons" /></strong>
          <span>${category1Actual}</span> {/* Display Category 1 actual total as Income */}
        </div>
        <div className="card">
          <strong>Budget <FaDollarSign className="icons" /></strong>
          <span>${totalBudget}</span>
        </div>
        <div className="card">
          <strong>Actual <FaCreditCard className="icons" /></strong>
          <span>${totalActual}</span>
        </div>
        <div className="card">
          <strong>Remaining <FaBuilding className="icons" /></strong>
          <span style={{ color: totalRemaining < 0 ? "red" : "green" }}>${totalRemaining}</span>
        </div>
      </div>

      {/* Toggle Button with Dropdown Icon */}
      <div className="toggle-button-row">
        <button onClick={handleToggleAllTables} className="toggle-button">
          {showAllTables ? <FaChevronDown /> : <FaChevronUp />}
        </button>
        <button className="add-category-button" onClick={handlePopupToggle}>
          <FaPlus /> {/* "+" Icon */}
        </button>
      </div>

      {/* Popup for adding budget */}
      {isPopupOpen && (
       <BudgetPopupComponent
       budgetData={budgetData}
       onSave={handleSaveBudget}
       onClose={handlePopupToggle}
     />
      )}


      {/* Budget Table */}
      <div className="budget-table">
        <div className="header-row">
          <span style={{ textAlign: "start" }}>Category</span>
          <span>Budget</span>
          <span>Actual</span>
          <span>Remaining</span>
        </div>
        {budgetData.length > 0 ? (
          budgetData.map((category) => {
            const nonZeroSubs = category.category?.subCategories?.filter((sub) => sub.budget !== 0);
            const zeroSubs = category.category?.subCategories?.filter((sub) => sub.budget === 0);
            const isExpanded = expandedCategories[category.id] ?? showAllTables;
            const iconKey = category.category?.categoryIconKey;
            const IconComponent = iconMapping[iconKey] || FaQuestionCircle;
            return (
              <React.Fragment key={category.id}>
                <div className="header-row1" onClick={() => handleToggleCategory(category.id)}>
                  <strong style={{ width: "24.5%", marginLeft: "10px", paddingTop: "5px" }}>
                    <IconComponent style={{ marginRight: "8px" }} />
                    {category.category?.category || "Unknown"}
                  </strong>
                  <span>
                    {(category.category?.allocated || 0) +
                      (category.category?.subCategories?.reduce((acc, sub) => acc + (sub.budget || 0), 0) || 0)}
                  </span>
                  <span>
                    {(category.category?.actual || 0) +
                      (category.category?.subCategories?.reduce((acc, sub) => acc + (sub.spent || 0), 0) || 0)}
                  </span>
                  <span style={{ color: category.category?.remaining < 0 ? "red" : "green" }}>
                    {(category.category?.remaining || 0) +
                      (category.category?.subCategories?.reduce((acc, sub) => acc + (sub.remaining || 0), 0) || 0)}
                  </span>
                </div>
                <div className="category-row">
                  {isExpanded && category.category?.subCategories?.length > 0 && (
                    <>
                      {nonZeroSubs?.map((sub) => {
                        const subIconKey = sub.icon_key;
                        const SubIconComponent = iconMapping[subIconKey] || FaQuestionCircle;
                        const isEditing = editingSubcategory === sub.compositeKey;
                        return (
                          <div key={sub.compositeKey} className="subcategory-row">
                            <div className="subcategory-name">
                              <SubIconComponent style={{ marginRight: "8px" }} />
                              {sub.subCategory || sub.customSubCategory || "N/A"}
                            </div>
                            <div
                              className="subcategory-budget"
                              onDoubleClick={() => handleBudgetDoubleClick(sub)}
                              contentEditable={isEditing}
                              suppressContentEditableWarning={true}
                              onInput={handleBudgetChange}
                              onBlur={() => saveBudgetChange(category.id, sub.compositeKey)}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  e.preventDefault();
                                  saveBudgetChange(category.id, sub.compositeKey);
                                }
                              }}
                              style={{ cursor: "pointer", outline: isEditing ? "1px solid blue" : "none" }}
                            >
                              {sub.budget || 0}
                            </div>
                            <div className="subcategory-spent">{sub.spent || 0}</div>
                            <div
                              className="subcategory-remaining"
                              style={{ color: (sub.budget - (sub.spent || 0)) < 0 ? "red" : "green" }}
                            >
                              {sub.budget - (sub.spent || 0)}
                            </div>
                          </div>
                        );
                      })}
                      {zeroSubs?.length > 0 && !showZeroBudget[category.id] && (
                        <div
                          className="subcategory-row zero-budget-toggle"
                          onClick={() => handleToggleZeroBudget(category.id)}
                          style={{
                            gridColumn: "1 / -1",
                            textAlign: "center",
                            cursor: "pointer",
                            fontStyle: "italic",
                            color: "blue",
                          }}
                        >
                          <FaPlus style={{ marginRight: "8px" }} />
                          {/* Show zero budget subcategories */}
                        </div>
                      )}
                      {zeroSubs?.length > 0 && showZeroBudget[category.id] && (
                        <>
                          {zeroSubs.map((sub) => {
                            const subIconKey = sub.icon_key;
                            const SubIconComponent = iconMapping[subIconKey] || FaQuestionCircle;
                            const isEditing = editingSubcategory === sub.compositeKey;
                            return (
                              <div key={sub.compositeKey} className="subcategory-row">
                                <div className="subcategory-name">
                                  <SubIconComponent style={{ marginRight: "8px" }} />
                                  {sub.subCategory || sub.customSubCategory || "N/A"}
                                </div>
                                <div
                                  className="subcategory-budget"
                                  onDoubleClick={() => handleBudgetDoubleClick(sub)}
                                  contentEditable={isEditing}
                                  suppressContentEditableWarning={true}
                                  onInput={handleBudgetChange}
                                  onBlur={() => saveBudgetChange(category.id, sub.compositeKey)}
                                  onKeyDown={(e) => {
                                    if (e.key === "Enter") {
                                      e.preventDefault();
                                      saveBudgetChange(category.id, sub.compositeKey);
                                    }
                                  }}
                                  style={{ cursor: "pointer", outline: isEditing ? "1px solid blue" : "none" }}
                                >
                                  {sub.budget || 0}
                                </div>
                                <div className="subcategory-spent">{sub.spent || 0}</div>
                                <div
                                  className="subcategory-remaining"
                                  style={{ color: (sub.budget - (sub.spent || 0)) < 0 ? "red" : "green" }}
                                >
                                  {sub.budget - (sub.spent || 0)}
                                </div>
                              </div>
                            );
                          })}
                          <div
                            className="subcategory-row zero-budget-toggle"
                            onClick={() => handleToggleZeroBudget(category.id)}
                            style={{
                              gridColumn: "1 / -1",
                              textAlign: "center",
                              cursor: "pointer",
                              fontStyle: "italic",
                              color: "blue",
                            }}
                          >
                            <FaMinus style={{ marginRight: "8px" }} />
                            {/* Hide zero budget subcategories */}
                          </div>
                        </>
                      )}
                    </>
                  )}
                </div>
              </React.Fragment>
            );
          })
        ) : (
          <p>No budget data available.</p>
        )}
      </div>
    </div>
  );
};




const BudgetPopupComponent = ({ onSave, onClose }) => {
  const dispatch = useDispatch();
  const { currentMonth, currentYear } = useSelector((state) => state.budget);

  // Common select style for closed dropdown (used for Category)
  const selectStyle = {
    width: "100%",
    padding: "0.5rem 0.75rem",
    border: "1px solid #e5e7eb",
    borderRadius: "0.375rem",
    fontSize: "0.875rem",
    boxSizing: "border-box",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minHeight: "40px",
    cursor: "pointer",
    position: "relative",
  };

  // Dropdown container style (for Category)
  const dropdownContainerStyle = {
    position: "absolute",
    top: "100%",
    left: 0,
    width: "100%",
    marginTop: "0.25rem",
    border: "1px solid #e5e7eb",
    borderRadius: "0.375rem",
    backgroundColor: "#fff",
    boxSizing: "border-box",
    padding: 0,
    zIndex: 999,
    maxHeight: "150px",
    overflowY: "auto",
  };

  // Dropdown item style (for Category)
  const dropdownItemStyle = {
    display: "flex",
    alignItems: "center",
    width: "100%",
    minHeight: "40px",
    padding: "0.5rem 0.75rem",
    fontSize: "0.875rem",
    boxSizing: "border-box",
    cursor: "pointer",
    border: "none",
    borderRadius: 0,
  };

  // State declarations
  const [selectedCategory, setSelectedCategory] = useState("");
  const [categories, setCategories] = useState([]);
  // selectedSubcategory is updated as the user types in the subcategory input.
  const [selectedSubcategory, setSelectedSubcategory] = useState("");
  const [newBudgetAmount, setNewBudgetAmount] = useState(0);
  const [isRollover, setIsRollover] = useState(false);
  const [isExcluded, setIsExcluded] = useState(false);
  const [isEditingBudget, setIsEditingBudget] = useState(false);
  // newSubcategory holds the text in the single subcategory input box.
  const [newSubcategory, setNewSubcategory] = useState("");
  const [subcategories, setSubcategories] = useState([]); // filtered for selected category
  const [allSubcategories, setAllSubcategories] = useState([]); // full list from backend

  // Dropdown toggle for Category only
  const [categoryDropdownOpen, setCategoryDropdownOpen] = useState(false);

  // Icon dropdown for new subcategory remains for icon selection.
  const [iconDropdownOpen, setIconDropdownOpen] = useState(false);
  const [selectedIconForNewSub, setSelectedIconForNewSub] = useState("FaMiscellaneous");
  const availableIcons = Object.keys(iconMapping);

  useEffect(() => {
    fetchCategories();
    fetchSubcategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await axiosInstance.get('/pennypal/api/v1/categories/all');
      setCategories(response.data);
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const fetchSubcategories = async () => {
    try {
      const response = await axiosInstance.get('/pennypal/api/v1/subcategories/all');
      setAllSubcategories(response.data);
    } catch (error) {
      console.error("Error fetching subcategories:", error);
    }
  };

  // Filter subcategories based on the selected category's id
  useEffect(() => {
    if (selectedCategory) {
      const filteredSubs = allSubcategories.filter(
        (sub) =>
          sub.category &&
          sub.category.id.toString() === selectedCategory.toString()
      );
      setSubcategories(filteredSubs);
    } else {
      setSubcategories([]);
    }
  }, [selectedCategory, allSubcategories]);

  // Slider change handler
  const handleSliderChange = (e) => {
    setNewBudgetAmount(parseInt(e.target.value, 10) || 0);
  };

  // When the user adds a new subcategory (e.g. on blur or via save), add it to the lists.
  const handleAddSubcategory = () => {
    if (newSubcategory.trim() !== "") {
      const newSub = {
        id: Date.now(), // temporary id
        category: { id: parseInt(selectedCategory, 10) },
        subCategory: newSubcategory,
        iconKey: selectedIconForNewSub,
      };
      setSubcategories([...subcategories, newSub]);
      setAllSubcategories([...allSubcategories, newSub]);
      // The input's value is already used for selectedSubcategory.
      setNewSubcategory("");
      setIconDropdownOpen(false);
    }
  };

  // Save the budget. If the subcategory doesn't exist yet, add it.
  const handleAddToBudget = async () => {
    if (!selectedCategory || !selectedSubcategory) {
      alert("Please select a category and enter a subcategory.");
      return;
    }
    const catObj = categories.find(
      (c) => c.id.toString() === selectedCategory.toString()
    );
    let subObj = subcategories.find(
      (s) => s.subCategory === selectedSubcategory
    );
    if (!subObj) {
      // Add the new subcategory if it doesn't exist.
      setNewSubcategory(selectedSubcategory);
      handleAddSubcategory();
      subObj = {
        id: Date.now(),
        category: { id: parseInt(selectedCategory, 10) },
        subCategory: selectedSubcategory,
        iconKey: selectedIconForNewSub,
      };
    }

    // Format date as YYYY-MM-DD
    const dateStr = new Date(currentYear, currentMonth, 1).toLocaleDateString("en-CA");

    const budgetPayload = {
      categoryId: catObj.id,
      subCategoryId: subObj.id || null,
      customSubCategory: selectedSubcategory,
      allocated: newBudgetAmount,
      actual: 100, // adjust logic as needed
      isRollover,
      isExcluded,
      userId: 1,
      date: dateStr,
      icon: subObj.iconKey,
    };

    try {
      const response = await axiosInstance.post('/pennypal/api/v1/budget/add', budgetPayload);
      const savedBudget = response.data;
      onSave(savedBudget);
      onClose();
    } catch (error) {
      console.error("Error saving budget:", error);
      if (error.response) {
        // Handle specific error responses from server
        alert(`Failed to save budget: ${error.response.data.message || 'Unknown error'}`);
      } else {
        alert("An error occurred while saving the budget.");
      }
    }
  };

  // Inline styles (or you can use CSS classes)
  const styles = {
    overlay: {
      position: "fixed",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
    },
    container: {
      background: "white",
      borderRadius: "0.5rem",
      boxShadow:
        "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
      width: "95%",
      maxWidth: "28rem",
      maxHeight: "85vh",
      position: "relative",
    },
    header: {
      padding: "1rem",
      backgroundColor: "#8BC34A",
      color: "white",
      position: "relative",
    },
    title: {
      fontSize: "1.25rem",
      fontWeight: "bold",
      margin: 0,
    },
    closeIcon: {
      position: "absolute",
      top: "1rem",
      right: "1rem",
      cursor: "pointer",
      color: "white",
      fontSize: "1.25rem",
    },
    content: {
      padding: "1.5rem",
      position: "relative",
    },
    formGroup: {
      marginBottom: "1.5rem",
      position: "relative",
    },
    label: {
      display: "block",
      fontSize: "0.875rem",
      fontWeight: "500",
      marginBottom: "0.5rem",
    },
    sliderContainer: {
      marginBottom: "1.5rem",
      display: "flex",
      flexDirection: "column",
    },
    sliderValue: {
      fontWeight: "500",
      cursor: "pointer",
    },
    slider: {
      width: "100%",
      height: "8px",
      borderRadius: "5px",
      appearance: "none",
      background: "#8BC34A",
      outline: "none",
    },
    optionGroup: {
      marginBottom: "1rem",
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
    },
    switchContainer: {
      position: "relative",
      display: "inline-block",
      width: "44px",
      height: "24px",
    },
    switchInput: {
      opacity: 0,
      width: 0,
      height: 0,
    },
    switchSlider: {
      position: "absolute",
      cursor: "pointer",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "#ccc",
      transition: ".4s",
      borderRadius: "34px",
    },
    switchSliderBefore: {
      position: "absolute",
      content: '""',
      height: "18px",
      width: "18px",
      left: "3px",
      bottom: "3px",
      backgroundColor: "white",
      transition: ".4s",
      borderRadius: "50%",
      transform: "translateX(0)",
    },
    switchSliderChecked: {
      backgroundColor: "#8BC34A",
    },
    switchSliderBeforeChecked: {
      transform: "translateX(20px)",
    },
    footer: {
      padding: "1rem 1.5rem",
      borderTop: "1px solid #e5e7eb",
      display: "flex",
      justifyContent: "center",
    },
    saveButton: {
      flex: 1,
      padding: "0.5rem 1rem",
      border: "none",
      borderRadius: "0.375rem",
      backgroundColor: "#8BC34A",
      color: "white",
      cursor: "pointer",
      maxWidth: "200px",
      textAlign: "center",
    },
    newSubcategoryContainer: {
      display: "flex",
      alignItems: "center",
      gap: "8px",
      marginBottom: "1.5rem",
      position: "relative",
    },
    iconBox: {
      cursor: "pointer",
      border: "1px solid #e5e7eb",
      borderRadius: "0.375rem",
      width: "40px",
      height: "40px",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      position: "relative",
    },
    iconDropdownMenu: {
      position: "absolute",
      top: "110%",
      left: 0,
      background: "white",
      border: "1px solid #e5e7eb",
      borderRadius: "0.375rem",
      padding: "0.5rem",
      zIndex: 1000,
      display: "grid",
      gridTemplateColumns: "repeat(5, 1fr)",
      gap: "4px",
      maxHeight: "50px",
      overflowY: "hidden",
    },
  };

  return (
    <div style={styles.overlay} className="popup-overlay">
      <div style={styles.container} className="popup-container">
        {/* Header */}
        <div style={styles.header}>
          <h3 style={styles.title} className="popup-title">
            Budget Details
          </h3>
          <div style={styles.closeIcon} onClick={onClose}>
            <FaTimes />
          </div>
        </div>

        {/* Content */}
        <div style={styles.content}>
          {/* Category */}
          <div style={styles.formGroup}>
            <label style={styles.label}>Category</label>
            <div
              onClick={() => setCategoryDropdownOpen(!categoryDropdownOpen)}
              style={selectStyle}
            >
              {selectedCategory ? (
                (() => {
                  const cat = categories.find(
                    (c) => c.id.toString() === selectedCategory.toString()
                  );
                  if (cat) {
                    const IconComp =
                      iconMapping[cat.categoryIconKey] || FaQuestionCircle;
                    return (
                      <>
                        <IconComp style={{ marginRight: "8px" }} />
                        {cat.category}
                      </>
                    );
                  }
                  return "Select a Category";
                })()
              ) : (
                "Select a Category"
              )}
              <span className="arrow">&#9662;</span>
            </div>
            {categoryDropdownOpen && (
              <div style={dropdownContainerStyle}>
                {categories.map((cat) => {
                  const IconComp =
                    iconMapping[cat.categoryIconKey] || FaQuestionCircle;
                  return (
                    <div
                      key={cat.id}
                      style={dropdownItemStyle}
                      onClick={() => {
                        setSelectedCategory(cat.id);
                        setCategoryDropdownOpen(false);
                        // Clear any previous subcategory value.
                        setSelectedSubcategory("");
                        setNewSubcategory("");
                      }}
                    >
                      <IconComp style={{ marginRight: "8px" }} />
                      {cat.category}
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Single Subcategory Input */}
          {selectedCategory && (
            <div style={styles.newSubcategoryContainer}>
              <input
                type="text"
                value={newSubcategory}
                onChange={(e) => {
                  setNewSubcategory(e.target.value);
                  setSelectedSubcategory(e.target.value);
                }}
                placeholder="Type Subcategory Name"
                style={{ ...selectStyle, justifyContent: "flex-start" }}
              />
              <div
                style={styles.iconBox}
                onClick={() => setIconDropdownOpen(!iconDropdownOpen)}
              >
                {(() => {
                  const SelectedIcon =
                    iconMapping[selectedIconForNewSub] || FaQuestionCircle;
                  return <SelectedIcon />;
                })()}
                {iconDropdownOpen && (
                  <div style={styles.iconDropdownMenu}>
                    {availableIcons.map((iconKey) => {
                      const IconComp =
                        iconMapping[iconKey] || FaQuestionCircle;
                      return (
                        <div
                          key={iconKey}
                          onClick={() => {
                            setSelectedIconForNewSub(iconKey);
                            setIconDropdownOpen(false);
                          }}
                          style={{ cursor: "pointer", padding: "0.25rem" }}
                        >
                          <IconComp />
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Budget Amount Slider */}
          <div style={styles.sliderContainer}>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <label style={styles.label}>Budget Amount</label>
              {isEditingBudget ? (
                <input
                  type="number"
                  value={newBudgetAmount}
                  onChange={(e) =>
                    setNewBudgetAmount(Number(e.target.value) || 0)
                  }
                  onBlur={() => setIsEditingBudget(false)}
                  style={{
                    ...styles.sliderValue,
                    textAlign: "right",
                    border: "1px solid #ccc",
                    borderRadius: "4px",
                    padding: "0.25rem",
                    width: "100px",
                    marginLeft: "1rem",
                  }}
                />
              ) : (
                <span
                  style={{
                    ...styles.sliderValue,
                    marginLeft: "1rem",
                    cursor: "pointer",
                  }}
                  onClick={() => setIsEditingBudget(true)}
                >
                  ${newBudgetAmount.toLocaleString()}
                </span>
              )}
            </div>
            <input
              type="range"
              min="0"
              max="10000"
              step="100"
              value={newBudgetAmount}
              onChange={handleSliderChange}
              style={styles.slider}
            />
          </div>

          {/* Rollover & Exclusion Toggles */}
          <div style={styles.optionGroup}>
            <label style={styles.label}>Make this a rollover budget</label>
            <div style={styles.switchContainer}>
              <input
                type="checkbox"
                checked={isRollover}
                onChange={() => setIsRollover(!isRollover)}
                style={styles.switchInput}
              />
              <span
                style={{
                  ...styles.switchSlider,
                  ...(isRollover ? styles.switchSliderChecked : {}),
                }}
                onClick={() => setIsRollover(!isRollover)}
              >
                <span
                  style={{
                    ...styles.switchSliderBefore,
                    ...(isRollover ? styles.switchSliderBeforeChecked : {}),
                  }}
                />
              </span>
            </div>
          </div>

          <div style={styles.optionGroup}>
            <label style={styles.label}>Exclude from budget</label>
            <div style={styles.switchContainer}>
              <input
                type="checkbox"
                checked={isExcluded}
                onChange={() => setIsExcluded(!isExcluded)}
                style={styles.switchInput}
              />
              <span
                style={{
                  ...styles.switchSlider,
                  ...(isExcluded ? styles.switchSliderChecked : {}),
                }}
                onClick={() => setIsExcluded(!isExcluded)}
              >
                <span
                  style={{
                    ...styles.switchSliderBefore,
                    ...(isExcluded ? styles.switchSliderBeforeChecked : {}),
                  }}
                />
              </span>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div style={styles.footer}>
          <button style={styles.saveButton} onClick={handleAddToBudget}>
            Save Budget
          </button>
        </div>
      </div>
    </div>
  );
};



export default Budget;
