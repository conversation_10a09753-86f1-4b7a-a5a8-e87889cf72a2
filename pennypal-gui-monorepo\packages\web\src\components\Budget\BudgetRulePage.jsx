/*
import React, { useEffect, useRef, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { FaPlus, FaEdit, FaTrash, FaInfoCircle } from 'react-icons/fa';
import {
  toggleCreateRuleModal,
  updateNewRule,
  resetNewRule,
  setLoading,
  clearMessages,
  createBudgetRule,
  setEditingRule,
} from '../../../../logic/redux/budgetRulesSlice';
import { themeClasses } from '../../utils/tailwindUtils'; // Adjust path as needed

const BudgetRulePage = ({darkMode}) => {
  const dispatch = useDispatch();
  const {
    budgetRules,
    isCreateRuleModalOpen,
    subcategories,
    newBudgetRule,
    loading,
    error,
    successMessage,
    editingRule,
    accounts,
  } = useSelector((state) => state.budgetRule);

  const [formErrors, setFormErrors] = useState({
    merchantNamePattern: '',
    thresholdAmount: '',
    fromSubCategoryId: '',
    accountId: '',
    toSubCategoryId: '',
    renamedMerchant: '',
    general: '',
  });

  const [selectedFields, setSelectedFields] = useState({
    merchantNamePattern: false,
    thresholdAmount: false,
    fromSubCategoryId: false,
    renamedMerchant: false,
    cascadeFlag: false,
    accountId: false,
  });

  const [conditionType, setConditionType] = useState('MERCHANT');
  const [tooltip, setTooltip] = useState({ open: false, rule: null, x: 0, y: 0 });
  const previousFocus = useRef(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState({
    open: false,
    ruleId: null,
    isDeleting: false,
  });

  useEffect(() => {
    setLoading(true);
    dispatch({ type: 'budgetRule/fetchSubcategories', payload: 1 });
    dispatch({ type: 'budgetRule/fetchBudgetRules', payload: 1 });
    dispatch({ type: 'budgetRule/fetchAccounts', payload: 1 });
  }, [dispatch]);

  useEffect(() => {
    if (successMessage === 'Rule deleted successfully') {
      const timer = setTimeout(() => {
        setDeleteConfirmation({ open: false, ruleId: null, isDeleting: false });
        dispatch(clearMessages());
      }, 2000);
      return () => clearTimeout(timer);
    }
    if (successMessage && successMessage !== 'Rule deleted successfully' && isCreateRuleModalOpen && !loading && !error) {
      const timer = setTimeout(() => {
        // Reset form fields and state without closing modal
        dispatch(resetNewRule());
        dispatch(setEditingRule(null));
        dispatch(clearMessages());
        setFormErrors({
          merchantNamePattern: '',
          thresholdAmount: '',
          fromSubCategoryId: '',
          accountId: '',
          toSubCategoryId: '',
          renamedMerchant: '',
          general: '',
        });
        setSelectedFields({
          merchantNamePattern: false,
          thresholdAmount: false,
          fromSubCategoryId: false,
          renamedMerchant: false,
          cascadeFlag: false,
          accountId: false,
        });
        setConditionType('MERCHANT');
      }, 2000);
      return () => clearTimeout(timer);
    }
    if (error && deleteConfirmation.isDeleting) {
      setDeleteConfirmation((prev) => ({ ...prev, isDeleting: false }));
    }
  }, [successMessage, error, loading, dispatch, isCreateRuleModalOpen]);

  const handleCloseRuleModal = () => {
    dispatch(toggleCreateRuleModal(false));
    dispatch(resetNewRule());
    dispatch(setEditingRule(null));
    dispatch(clearMessages());
    setFormErrors({
      merchantNamePattern: '',
      thresholdAmount: '',
      fromSubCategoryId: '',
      accountId: '',
      toSubCategoryId: '',
      renamedMerchant: '',
      general: '',
    });
    setSelectedFields({
      merchantNamePattern: false,
      thresholdAmount: false,
      fromSubCategoryId: false,
      renamedMerchant: false,
      cascadeFlag: false,
      accountId: false,
    });
    setConditionType('MERCHANT');
  };

  const handleRuleInputChange = (field) => (event) => {
    const value = field === 'merchantNamePattern' || field === 'renamedMerchant' || field === 'tags' || field === 'goal'
      ? event.target.value
      : Number(event.target.value);
    dispatch(updateNewRule({ [field]: value }));
  };

  const handleCategoryChange = (field) => (e) => {
    if (field == 'fromSubCategoryId') {
      const fromSubCatId = Number(e.target.value);
      const selectedSubcategory = subcategories.find((subcat) => subcat.subCategoryId === fromSubCatId);
      
      dispatch(
        updateNewRule({
          fromSubCategoryId: fromSubCatId,
          fromCategoryId: selectedSubcategory.categoryId,
        })
      );
    }

    if (field == 'toSubCategoryId') {
      const toSubCatId = Number(e.target.value);
      const selectedSubcategory = subcategories.find((subcat) => subcat.subCategoryId === toSubCatId);
      
      dispatch(
        updateNewRule({
          toSubCategoryId: toSubCatId,
          toCategoryId: selectedSubcategory.categoryId,
        })
      );
    }

    if (field === 'fromSubCategoryId' || field === 'toSubCategoryId') {
      validateCategoryMatch();
    }
  }

  const handleFieldToggle = (field) => {
    setSelectedFields((prev) => {
      const newSelected = { ...prev, [field]: !prev[field] };
      if (!newSelected[field]) {
        if (field === 'thresholdAmount') {
          dispatch(updateNewRule({ thresholdAmount: 0, amountType: null, amountMatch: null }));
        } else if (field === 'renamedMerchant') {
          dispatch(updateNewRule({ renamedMerchant: '' }));
        } else if (field === 'cascadeFlag') {
          dispatch(updateNewRule({ cascadeFlag: false }));
        } else if (field === 'merchantNamePattern') {
          dispatch(updateNewRule({ merchantNamePattern: null, merchantMatchRegex: false }));
        }
        setFormErrors((prev) => ({ ...prev, [field]: '' }));
      } else if (field === 'thresholdAmount') {
        dispatch(updateNewRule({
          amountType: newBudgetRule.amountType || 'credit',
          amountMatch: newBudgetRule.amountMatch || 'greater',
        }));
      }
      return newSelected;
    });
  };

  const validateCategoryMatch = () => {
    const fromCategoryId = newBudgetRule.fromSubCategoryId
      ? subcategories.find((subcat) => subcat.subCategoryId === newBudgetRule.fromSubCategoryId)?.categoryId
      : null;
    const toCategoryId = newBudgetRule.toSubCategoryId
      ? subcategories.find((subcat) => subcat.subCategoryId === newBudgetRule.toSubCategoryId)?.categoryId
      : null;
    if (selectedFields.fromSubCategoryId && fromCategoryId && toCategoryId && fromCategoryId !== toCategoryId) {
      setFormErrors((prev) => ({
        ...prev,
        fromSubCategoryId: 'Source category ID must match target category ID',
      }));
    } else {
      setFormErrors((prev) => ({ ...prev, fromSubCategoryId: '' }));
    }
  };

  const handleSaveRule = () => {
    setFormErrors({
      merchantNamePattern: '',
      thresholdAmount: '',
      fromSubCategoryId: '',
      accountId: '',
      toSubCategoryId: '',
      renamedMerchant: '',
      general: '',
    });

    let hasErrors = false;

    if (
      !selectedFields.merchantNamePattern &&
      !selectedFields.thresholdAmount &&
      !selectedFields.fromSubCategoryId &&
      !selectedFields.accountId
    ) {
      setFormErrors((prev) => ({
        ...prev,
        general: 'At least one of Merchants, Amount, Source Category, or Account must be selected',
      }));
      hasErrors = true;
    }

    if (selectedFields.merchantNamePattern && !newBudgetRule.merchantNamePattern?.trim()) {
      setFormErrors((prev) => ({
        ...prev,
        merchantNamePattern: 'Merchant name is required',
      }));
      hasErrors = true;
    }

    if (selectedFields.thresholdAmount && (!newBudgetRule.thresholdAmount || newBudgetRule.thresholdAmount <= 0)) {
      setFormErrors((prev) => ({
        ...prev,
        thresholdAmount: 'Amount is required and must be greater than 0',
      }));
      hasErrors = true;
    }

    if (selectedFields.fromSubCategoryId && !newBudgetRule.fromSubCategoryId) {
      setFormErrors((prev) => ({
        ...prev,
        fromSubCategoryId: 'Source subcategory is required',
      }));
      hasErrors = true;
    }

    if (selectedFields.accountId && !newBudgetRule.accountId) {
      setFormErrors((prev) => ({
        ...prev,
        accountId: 'Account is required',
      }));
      hasErrors = true;
    }

    if (!newBudgetRule.toSubCategoryId) {
      setFormErrors((prev) => ({
        ...prev,
        toSubCategoryId: 'Target subcategory is required',
      }));
      hasErrors = true;
    }

    if (selectedFields.renamedMerchant && !newBudgetRule.renamedMerchant?.trim()) {
      setFormErrors((prev) => ({
        ...prev,
        renamedMerchant: 'Renamed merchant is required',
      }));
      hasErrors = true;
    }

    validateCategoryMatch();
    if (formErrors.fromSubCategoryId) hasErrors = true;

    if (selectedFields.fromSubCategoryId) {
      if (newBudgetRule.fromCategoryId != newBudgetRule.toCategoryId) {
        setFormErrors((prev) => ({
          ...prev,
          fromSubCategoryId: 'Source category ID must match target category ID',
        }));
        hasErrors = true;
      }
    }

    if (hasErrors) {
      dispatch(setLoading(false));
      return;
    }

    dispatch(setLoading(true));
    const ruleData = {
      ...newBudgetRule,
      merchantMatchRegex: selectedFields.merchantNamePattern ? (newBudgetRule.merchantMatchRegex ? true : false) : false,
      merchantNamePattern: selectedFields.merchantNamePattern ? newBudgetRule.merchantNamePattern : null,
      amountType: selectedFields.thresholdAmount ? (newBudgetRule.amountType || 'credit') : null,
      amountMatch: selectedFields.thresholdAmount ? (newBudgetRule.amountMatch || 'greater') : null,
      thresholdAmount: selectedFields.thresholdAmount ? newBudgetRule.thresholdAmount : 0,
      fromSubCategoryId: selectedFields.fromSubCategoryId ? newBudgetRule.fromSubCategoryId : null,
      fromCategoryId: selectedFields.fromSubCategoryId
        ? newBudgetRule.fromCategoryId
        : newBudgetRule.toCategoryId,
      accountId: selectedFields.accountId ? newBudgetRule.accountId : null,
      renamedMerchant: selectedFields.renamedMerchant ? newBudgetRule.renamedMerchant : null,
      cascadeFlag: newBudgetRule.cascadeFlag || false,
      hideTransactionsFlag: newBudgetRule.hideTransactionsFlag || false,
      tags: newBudgetRule.tags || null,
      goal: newBudgetRule.goal || null,
      conditionType,
      isActive: true,
      ruleType: newBudgetRule.ruleType || 'CUSTOM',
    };
    dispatch(createBudgetRule(ruleData));
  };

  const handleDelete = (ruleId) => {
    previousFocus.current = document.activeElement;
    setDeleteConfirmation({ open: true, ruleId, isDeleting: false });
  };

  const handleConfirmDelete = () => {
    setDeleteConfirmation((prev) => ({ ...prev, isDeleting: true }));
    dispatch({ type: 'budgetRule/deleteBudgetRule', payload: deleteConfirmation.ruleId });
  };

  const handleCancelDelete = () => {
    setDeleteConfirmation({ open: false, ruleId: null, isDeleting: false });
    dispatch(clearMessages());
  };

  const handleEdit = (rule) => {
    dispatch(setEditingRule(rule));
    dispatch(toggleCreateRuleModal(true));
    setConditionType(rule.conditionType || 'MERCHANT');
    setSelectedFields({
      merchantNamePattern: !!rule.merchantNamePattern,
      thresholdAmount: rule.thresholdAmount > 0,
      fromSubCategoryId: !!rule.fromSubCategoryId,
      renamedMerchant: !!rule.renamedMerchant,
      cascadeFlag: !!rule.cascadeFlag,
      accountId: !!rule.accountId,
    });
    dispatch(
      updateNewRule({
        id: rule.id,
        userId: rule.userId,
        ruleType: rule.ruleType,
        fromCategoryId: rule.fromCategoryId,
        fromSubCategoryId: rule.fromSubCategoryId,
        toCategoryId: rule.toCategoryId,
        toSubCategoryId: rule.toSubCategoryId,
        conditionType: rule.conditionType,
        thresholdAmount: rule.thresholdAmount,
        thresholdPercentage: rule.thresholdPercentage,
        transferDay: rule.transferDay,
        merchantNamePattern: rule.merchantNamePattern,
        isActive: rule.isActive,
        maxTransferAmount: rule.maxTransferAmount,
        minTransferAmount: rule.minTransferAmount,
        maxTransferPercent: rule.maxTransferPercent,
        minTransferPercent: rule.minTransferPercent,
        executionFrequency: rule.executionFrequency,
        renamedMerchant: rule.renamedMerchant || '',
        cascadeFlag: rule.cascadeFlag,
        merchantMatchRegex: rule.merchantMatchRegex || false,
        amountType: rule.amountType,
        amountMatch: rule.amountMatch,
        accountId: rule.accountId,
        hideTransactionsFlag: rule.hideTransactionsFlag,
        tags: rule.tags,
        goal: rule.goal,
      })
    );
  };

  const handleInfoClick = (event, rule) => {
    event.preventDefault();
    const rect = event.currentTarget.getBoundingClientRect();
    const scrollX = window.scrollX;
    const scrollY = window.scrollY;
    const tooltipWidth = 250;
    let x = rect.left + scrollX - tooltipWidth - 10;
    let y = rect.top + scrollY;
    if (x < scrollX) x = rect.left + scrollX + 20;
    if (y < scrollY) y = scrollY;
    setTooltip({ open: true, rule, x, y });
  };

  const handleCloseTooltip = (event) => {
    if ((event.type === 'keydown' && event.key === 'Escape') ||
       (event.type === 'click' && !event.target.closest('.tooltip'))){
      setTooltip({ open: false, rule: null, x: 0, y: 0 });
    }
  };

  useEffect(() => {
    if (tooltip.open) {
      const timeoutId = setTimeout(() => {
        document.addEventListener('click', handleCloseTooltip);
      }, 0);
      return () => {
        clearTimeout(timeoutId);
        document.removeEventListener('click', handleCloseTooltip);
      };
    }
  }, [tooltip.open]);

  return (
    <>
      
    <div className={`p-2 w-full min-h-screen ${themeClasses.pageContainer(darkMode)}`}>
        <div className="mb-6 flex justify-between items-center">
        <h1 className={`text-2xl `}>Budget Rules</h1>
          <button
          className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition ${themeClasses.reconcileButton(darkMode)}`}
            onClick={() => dispatch(toggleCreateRuleModal(true))}
          >
            <FaPlus className="w-2 h-2 mr-2 fill-current" aria-hidden="true" /> Create Rule
          </button>
        </div>
          <div className={`mt-6 shadow rounded-lg overflow-hidden $${themeClasses.tableContainer(darkMode)}`}>
            <table className="min-w-full">
          <thead className={`${themeClasses.tableHeaderRow(darkMode)}`}>
                <tr>
                  <th className={`px-4 py-3 text-left font-bold text-sm ${
                    darkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>Rule Name</th>
                  <th className={`px-4 py-3 text-left font-bold text-sm ${
                    darkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>Merchant</th>
                  <th className={`px-4 py-3 text-left font-bold text-sm ${
                    darkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>Amount</th>
                  <th className={`px-4 py-3 text-left font-bold text-sm ${
                    darkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>Account ID</th>
                  <th className={`px-4 py-3 text-left font-bold text-sm ${
                    darkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>From Category</th>
                  <th className={`px-4 py-3 text-left font-bold text-sm ${
                    darkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>To Category</th>
                  <th className={`px-4 py-3 text-left font-bold text-sm ${
                    darkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>Actions</th>
                </tr>
              </thead>
          <tbody className={`${themeClasses.tableBody(darkMode)}`}>
                {loading ? (
                  <tr>
                    <td colSpan={6} className="text-center py-4">Loading...</td>
                  </tr>
                ) : budgetRules.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="text-center py-4">No budget rules found</td>
                  </tr>
                ) : (
                  budgetRules.map((rule) => (
                     <tr key={rule.id} className={`border-t ${themeClasses.tableRow(darkMode)}`}>
                  <td className={`px-4 py-2 ${themeClasses.tableCell(darkMode)}`}>{rule.ruleType}</td>
                  <td className={`px-4 py-2 ${themeClasses.tableCell(darkMode)}`}>{rule.merchantNamePattern || '-'}</td>
                  <td className={`px-4 py-2 ${themeClasses.tableCell(darkMode)}`}>{rule.thresholdAmount || '-'}</td>
                  <td className={`px-4 py-2 ${themeClasses.tableCell(darkMode)}`}>{rule.accountName || '-'}</td>
                  <td className={`px-4 py-2 ${themeClasses.tableCell(darkMode)}`}>{rule.fromCatName || '-'}</td>
                  <td className={`px-4 py-2 ${themeClasses.tableCell(darkMode)}`}>{rule.toCatName || '-'}</td>
                  <td className="px-2 py-1 flex space-x-3 items-center">
                        <button
                          className="!text-gray-600 hover:text-gray-800 p-2 rounded-full hover:bg-gray-100 transition !visible !opacity-100"
                          onClick={(e) => handleInfoClick(e, rule)}
                          title="View Rule Details"
                          aria-label={`View details for rule ${rule.ruleType}`}
                        >
                          <FaInfoCircle className="w-4 h-4 fill-current" aria-hidden="true" />
                        </button>
                        <button
                          className="!text-blue-600 hover:text-blue-800 p-2 rounded-full hover:bg-blue-100 transition !visible !opacity-100"
                          onClick={() => handleEdit(rule)}
                          title="Edit Rule"
                          aria-label={`Edit rule ${rule.ruleType}`}
                        >
                          <FaEdit className="w-4 h-4 fill-current" aria-hidden="true" />
                        </button>
                        <button
                          className="!text-red-600 hover:text-red-800 p-2 rounded-full hover:bg-red-100 transition !visible !opacity-100"
                          onClick={() => handleDelete(rule.id)}
                          title="Delete Rule"
                          aria-label={`Delete rule ${rule.ruleType}`}
                          tabIndex={deleteConfirmation.open ? -1 : 0}
                        >
                          <FaTrash className="w-4 h-4 fill-current" aria-hidden="true" />
                        </button>
                      </td>
                    </tr>
                  )))}
              </tbody>
            </table>
          </div>

          {tooltip.open && tooltip.rule && (
            <div
              className="tooltip"
              style={{ left: tooltip.x, top: tooltip.y }}
              onClick={(e) => e.stopPropagation()}
              onKeyDown={handleCloseTooltip}
              tabIndex={0}
            >
              <table className="tooltip-table">
                <tbody>
                  <tr><td>Rename Flag</td><td>{tooltip.rule.renamedMerchant ? 'Yes' : 'No'}</td></tr>
                  <tr><td>Rename Merchant To</td><td>{tooltip.rule.renamedMerchant || '-'}</td></tr>
                  <tr><td>Cascade Flag</td><td>{tooltip.rule.cascadeFlag ? 'Yes' : 'No'}</td></tr>
                  <tr><td>Hide Transactions</td><td>{tooltip.rule.hideTransactionsFlag ? 'Yes' : 'No'}</td></tr>
                  <tr><td>Tags</td><td>{tooltip.rule.tags || '-'}</td></tr>
                  <tr><td>Goal</td><td>{tooltip.rule.goal || '-'}</td></tr>
                </tbody>
              </table>
            </div>
          )}

          {deleteConfirmation.open && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg w-full max-w-sm mx-4">
                <div className="p-6">
                  {deleteConfirmation.isDeleting ? (
                    successMessage === 'Rule deleted successfully' ? (
                      <div className="flex flex-col items-center gap-4">
                        <p className="text-green-700">Rule deleted successfully</p>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center gap-4" role="status">
                        <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                        <p>Deleting budget rule...</p>
                      </div>
                    )
                  ) : (
                    <>
                      <p className="text-gray-700">Are you sure you want to delete this budget rule?</p>
                      <p className="text-sm text-gray-500 mt-2">This action cannot be undone.</p>
                    </>
                  )}
                  {error && (
                    <div className="mt-4 p-3 bg-red-100 text-red-700 rounded flex justify-between items-center">
                      {error}
                      <button onClick={() => dispatch(clearMessages())} className="text-red-700 hover:text-red-900">
                        ×
                      </button>
                    </div>
                  )}
                </div>
                <div className="flex justify-center gap-4 p-4 border-t">
                  {!deleteConfirmation.isDeleting && (
                    <>
                      <button
                        onClick={handleCancelDelete}
                        className="px-4 py-2 border border-gray-300 rounded text-black hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        tabIndex={deleteConfirmation.open ? 0 : -1}
                        style={{ display: 'block', visibility: 'visible', opacity: 1 }}
                      >
                        Cancel
                      </button>
                      <button
                        onClick={handleConfirmDelete}
                        className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                        tabIndex={deleteConfirmation.open ? 0 : -1}
                        style={{ display: 'block', visibility: 'visible', opacity: 1 }}
                      >
                        Delete
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}

      {isCreateRuleModalOpen && (
        <div className={`fixed inset-0 flex items-center justify-center z-50 ${themeClasses.modalOverlay(darkMode)}`}>
          <div className={`rounded-lg w-full max-w-4xl mx-4 h-[90vh] flex flex-col ${themeClasses.modalContent(darkMode)}`}>
            <div className={`border-b p-4 text-center font-medium relative ${themeClasses.modalHeader(darkMode)}`}>
              {editingRule ? 'Edit Budget Rule' : 'Create Budget Rule'}
              <button
                className={`absolute right-4 top-4 ${themeClasses.closeButton(darkMode)}`}
                onClick={handleCloseRuleModal}
                disabled={loading}
                aria-label="Close modal"
              >
                <svg className={`w-6 h-6 ${themeClasses.icon(darkMode)}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="flex flex-1 overflow-hidden">
              <div className={`w-1/2 p-4 overflow-y-auto scrollbar-hidden ${themeClasses.modalPanelLeft(darkMode)}`}>
                <h3 className="text-md font-semibold mb-4">Conditions</h3>
                <div className="flex flex-col gap-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className={`h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
                      checked={selectedFields.merchantNamePattern}
                      onChange={() => {
                        handleFieldToggle('merchantNamePattern');
                        if (!selectedFields.merchantNamePattern) {
                          setFormErrors((prev) => ({ ...prev, merchantNamePattern: '' }));
                        }
                      }}
                    />
                    <span className={`ml-2 text-sm font-medium ${themeClasses.checkboxLabel(darkMode)}`}>Merchant</span>
                  </label>
                  {selectedFields.merchantNamePattern && (
                    <>
                      <div className="ml-6 mt-2 flex gap-2">
                        <select
                          className={`w-1/3 p-2 rounded text-sm ${themeClasses.selectField(darkMode)} ${themeClasses.inputFocusRing(darkMode)}`}
                          value={newBudgetRule.merchantMatchRegex ? 'contains' : 'equals'}
                          onChange={(e) => dispatch(updateNewRule({ merchantMatchRegex: e.target.value === 'contains' }))}
                        >
                          <option value="contains">Contains</option>
                          <option value="equals">Exact Match</option>
                        </select>
                            <input
                              type="text"
                              className={`w-2/3 p-2 border rounded text-sm ${themeClasses.inputField(darkMode)} ${themeClasses.inputFocusRing(darkMode)} ${formErrors.merchantNamePattern ? 'border-red-500' : ''}`}
                              value={newBudgetRule.merchantNamePattern || ''}
                              onChange={(e) => {
                                const value = e.target.value;
                                if (!value.trim()) {
                                  setFormErrors((prev) => ({ ...prev, merchantNamePattern: 'Merchant name is required' }));
                                } else {
                                  setFormErrors((prev) => ({ ...prev, merchantNamePattern: '' }));
                                }
                                handleRuleInputChange('merchantNamePattern')(e);
                              }}
                              placeholder="Enter merchant name"
                            />
                          </div>
                      <p className={`ml-6 text-xs mt-1 ${formErrors.merchantNamePattern ? themeClasses.errorText(darkMode) : themeClasses.noChartsText(darkMode)}`}>
                            {formErrors.merchantNamePattern || ''}
                          </p>
                        </>
                      )}

                      
                      <label className="flex items-center mt-2">
                        <input
                          type="checkbox"
                      className={`h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
                          checked={selectedFields.thresholdAmount}
                          onChange={() => {
                            handleFieldToggle('thresholdAmount');
                            if (!selectedFields.thresholdAmount) {
                              setFormErrors((prev) => ({ ...prev, thresholdAmount: '' }));
                            }
                          }}
                        />
                    <span className={`ml-2 text-sm font-medium ${themeClasses.checkboxLabel(darkMode)}`}>Amount</span>
                      </label>
                      {selectedFields.thresholdAmount && (
                        <>
                          <div className="ml-6 mt-2 flex gap-2">
                            <select
                          className={`w-1/3 p-2 rounded text-sm ${themeClasses.selectField(darkMode)} ${themeClasses.inputFocusRing(darkMode)}`}
                              value={newBudgetRule.amountType || 'credit'}
                              onChange={(e) => dispatch(updateNewRule({ amountType: e.target.value }))}
                            >
                              <option value="credit">Credit</option>
                              <option value="debit">Debit</option>
                            </select>
                            <select
                          className={`w-1/3 p-2 rounded text-sm ${themeClasses.selectField(darkMode)} ${themeClasses.inputFocusRing(darkMode)}`}
                              value={newBudgetRule.amountMatch || 'greater'}
                              onChange={(e) => dispatch(updateNewRule({ amountMatch: e.target.value }))}
                            >
                              <option value="equals">Equals</option>
                              <option value="greater">Greater than</option>
                              <option value="less">Less than</option>
                            </select>
                            <input
                              type="number"
                          className={`w-1/3 p-2 rounded text-sm ${themeClasses.inputField(darkMode)} ${themeClasses.inputFocusRing(darkMode)} ${formErrors.thresholdAmount ? 'border-red-500' : ''}`}
                              value={newBudgetRule.thresholdAmount || ''}
                              onChange={(e) => {
                                const value = e.target.value;
                                if (!value || value <= 0) {
                                  setFormErrors((prev) => ({ ...prev, thresholdAmount: 'Amount is required and must be greater than 0' }));
                                } else {
                                  setFormErrors((prev) => ({ ...prev, thresholdAmount: '' }));
                                }
                                handleRuleInputChange('thresholdAmount')(e);
                              }}
                              placeholder="Amount"
                            />
                          </div>
                          <p className={`ml-6 text-xs mt-1 ${formErrors.thresholdAmount ? 'text-red-500' : 'text-gray-500'}`}>
                            {formErrors.thresholdAmount || 'Enter amount threshold'}
                          </p>
                        </>
                      )}

                     
                      <label className="flex items-center mt-4">
                        <input
                          type="checkbox"
                      className={`h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
                          checked={selectedFields.fromSubCategoryId}
                          onChange={() => {
                            handleFieldToggle('fromSubCategoryId');
                            if (!selectedFields.fromSubCategoryId) {
                              setFormErrors((prev) => ({ ...prev, fromSubCategoryId: '' }));
                            }
                          }}
                        />
                    <span className={`ml-2 text-sm font-medium ${themeClasses.checkboxLabel(darkMode)}`}>Source Subcategory</span>
                      </label>

                      {selectedFields.fromSubCategoryId && (
                        <div className="ml-6 mt-2">
                          <select
                        className={`w-full p-2 rounded text-sm ${themeClasses.selectField(darkMode)} ${themeClasses.inputFocusRing(darkMode)} ${formErrors.fromSubCategoryId ? 'border-red-500' : ''}`}
                            value={newBudgetRule.fromSubCategoryId || ''}
                            onChange={(e) => {
                              const fromSubCatId = Number(e.target.value);
                              if (!fromSubCatId) {
                                setFormErrors((prev) => ({ ...prev, fromSubCategoryId: 'Source subcategory is required' }));
                              } else {
                                handleCategoryChange('fromSubCategoryId')(e);
                              }
                            }}
                          >
                            <option value="" disabled>Select source subcategory</option>
                            {subcategories && subcategories.length > 0 ? (
                              subcategories.map((subcat) => (
                                <option key={subcat.subCategoryId} value={subcat.subCategoryId}>
                                  {subcat.subCategoryName}
                                </option>
                              ))
                            ) : (
                              <option value="" disabled>No subcategories available</option>
                            )}
                          </select>
                      <p className={`text-xs mt-1 ${formErrors.fromSubCategoryId ? themeClasses.errorText(darkMode) : themeClasses.noChartsText(darkMode)}`}>
                            {formErrors.fromSubCategoryId || ""}
                          </p>
                        </div>
                      )}

                      <label className="flex items-center mt-4">
                        <input
                          type="checkbox"
                      className={`h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
                          checked={selectedFields.accountId}
                          onChange={() => handleFieldToggle('accountId')}
                        />
                    <span className={`ml-2 text-sm font-medium ${themeClasses.checkboxLabel(darkMode)}`}>Account</span>
                      </label>
                            
                      {selectedFields.accountId && (
                        <div className="ml-6 mt-2">
                          {loading ? (
                        <div className={`${themeClasses.loadingText(darkMode)}`}>Loading accounts...</div>
                          ) : (
                            <select
                          className={`w-full p-2 rounded text-sm ${themeClasses.selectField(darkMode)} ${themeClasses.inputFocusRing(darkMode)} ${formErrors.accountId ? 'border-red-500' : ''}`}
                              value={newBudgetRule.accountId || ''}
                              onChange={(e) => {
                                const accountId = Number(e.target.value);
                                dispatch(updateNewRule({ accountId }));
                                if (!accountId) {
                                  setFormErrors((prev) => ({ ...prev, accountId: 'Account is required' }));
                                } else {
                                  setFormErrors((prev) => ({ ...prev, accountId: '' }));
                                }
                              }}
                            >
                              <option value="">Select account</option>
                              {Array.isArray(accounts) && accounts.length > 0 ? (
                                accounts.map((account) => (
                                  <option key={account.account_id} value={account.account_id}>
                                    {account.account_name}
                                  </option>
                                ))
                              ) : (
                                <option value="" disabled>No accounts available</option>
                              )}
                            </select>
                          )}
                          {formErrors.accountId && 
                        <p className={`text-xs mt-1 ${themeClasses.errorText(darkMode)}`}>{formErrors.accountId}</p>
                          }
                        </div>
                      )}

                      {(!selectedFields.merchantNamePattern &&
                        !selectedFields.thresholdAmount &&
                        !selectedFields.fromSubCategoryId &&
                        !selectedFields.accountId) && (
                    <p className={`ml-6 text-xs mt-2 ${themeClasses.errorText(darkMode)}`}>
                          At least one of Merchants, Amount, Source Category, or Account must be selected
                        </p>
                      )}
                      <div className="mt-60 ml-1">
  <label className="flex items-center">
    <input
      type="checkbox"
                        className={`h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
      checked={newBudgetRule.cascadeFlag || false}
      onChange={(e) => dispatch(updateNewRule({ cascadeFlag: e.target.checked }))}
    />
                      <span className={`ml-2 text-sm ${themeClasses.checkboxLabel(darkMode)}`}>Run rules for past transactions</span>
  </label>
</div>

                    </div>
                  </div>

                 
                              <div className={`w-1/2 p-4 overflow-y-auto relative ${themeClasses.modalPanelRight(darkMode)}`}>
                    <h3 className="text-m font-bold">Apply</h3>
                    {loading && (
                  <div className={`absolute inset-0 bg-opacity-50 flex items-center justify-center z-10 ${themeClasses.loadingOverlay(darkMode)}`}>
                        <div className="flex items-center">
                        <div className={`w-8 h-8 border-4 rounded-full animate-spin mr-2 ${themeClasses.spinner(darkMode)}`} />
                                             <span className={`${themeClasses.loadingText(darkMode)}`}>Saving...</span>
                       
                        </div>
                      </div>
                    )}
                    {error && successMessage !== 'Rule deleted successfully' && (
                  <div className={`mb-4 p-3 rounded flex justify-between items-center ${themeClasses.error(darkMode)}`}>
                        {error}
                    <button onClick={() => dispatch(clearMessages())} className={`${themeClasses.errorText(darkMode)} hover:text-red-900`}>
                          ×
                        </button>
                      </div>
                    )}
                    {successMessage && successMessage !== 'Rule deleted successfully' && (
                  <div className={`mb-4 p-3 rounded flex justify-between items-center ${themeClasses.saveButtonSaved(darkMode)}`}>
                        {successMessage}
                    <button onClick={() => dispatch(clearMessages())} className={`${themeClasses.successText(darkMode)} hover:text-green-900`}>
                          ×
                        </button>
                      </div>
                    )}
                    <div className="flex flex-col gap-3 relative z-20 mt-4">
                     
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                      className={`h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
                          checked={selectedFields.renamedMerchant}
                          onChange={() => {
                            handleFieldToggle('renamedMerchant');
                            if (!selectedFields.renamedMerchant) {
                              setFormErrors((prev) => ({ ...prev, renamedMerchant: '' }));
                            }
                          }}
                        />
                    <span className={`ml-2 text-sm ${themeClasses.checkboxLabel(darkMode)}`}>Rename Merchant</span>
                      </label>
                      {selectedFields.renamedMerchant && (
                        <div>
                      <label className={`block text-sm font-medium ${themeClasses.checkboxLabel(darkMode)}`}>Renamed Merchant *</label>
                          <input
                            type="text"
                        className={`mt-1 w-full p-2 rounded text-sm ${themeClasses.inputField(darkMode)} ${themeClasses.inputFocusRing(darkMode)} ${formErrors.renamedMerchant ? 'border-red-500' : ''}`}
                            value={newBudgetRule.renamedMerchant || ''}
                            onChange={(e) => {
                              const value = e.target.value;
                              if (!value.trim()) {
                                setFormErrors((prev) => ({ ...prev, renamedMerchant: 'Renamed merchant is required' }));
                              } else {
                                setFormErrors((prev) => ({ ...prev, renamedMerchant: '' }));
                              }
                              handleRuleInputChange('renamedMerchant')(e);
                            }}
                            placeholder="Enter renamed merchant"
                          />
                      <p className={`text-xs mt-1 ${formErrors.renamedMerchant ? themeClasses.errorText(darkMode) : themeClasses.noChartsText(darkMode)}`}>
                            {formErrors.renamedMerchant || 'Enter renamed merchant to be saved'}
                          </p>
                        </div>
                      )}

                      
                      <div>
                    <label className={`block text-sm font-medium ${themeClasses.checkboxLabel(darkMode)}`}>Target Subcategory *</label>
                        <select
                      className={`mt-1 w-full p-2 rounded text-sm ${themeClasses.selectField(darkMode)} ${themeClasses.inputFocusRing(darkMode)} ${formErrors.toSubCategoryId ? 'border-red-500' : ''}`}
                          value={newBudgetRule.toSubCategoryId || ''}
                          onChange={(e) => {
                            const toSubCatId = Number(e.target.value);
                            const selectedSubcategory = subcategories.find((subcat) => subcat.subCategoryId === toSubCatId);
                            if (!toSubCatId) {
                              setFormErrors((prev) => ({ ...prev, toSubCategoryId: 'Target subcategory is required' }));
                            } else {
                              handleCategoryChange('toSubCategoryId')(e);
                            }
                          }}
                          required
                        >
                          <option value="" disabled>Select target subcategory</option>
                          {subcategories && subcategories.length > 0 ? (
                            subcategories.map((subcat) => (
                              <option key={subcat.subCategoryId} value={subcat.subCategoryId}>
                                {subcat.subCategoryName}
                              </option>
                            ))
                          ) : (
                            <option value="" disabled>No subcategories available</option>
                          )}
                        </select>
                    <p className={`text-xs mt-1 ${formErrors.toSubCategoryId ? themeClasses.errorText(darkMode) : themeClasses.noChartsText(darkMode)}`}>
                          {formErrors.toSubCategoryId || ''}
                        </p>
                      </div>

                
                      <label className="flex items-center mt-4">
                        <input
                          type="checkbox"
                      className={`h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
                          checked={newBudgetRule.hideTransactionsFlag || false}
                          onChange={(e) => dispatch(updateNewRule({ hideTransactionsFlag: e.target.checked }))}
                        />
                    <span className={`ml-2 text-sm ${themeClasses.checkboxLabel(darkMode)}`}>Hide transactions</span>
                      </label>

                      <div className="mt-4">
                    <label className={`block text-sm font-medium ${themeClasses.checkboxLabel(darkMode)}`}>Add Tags</label>
                        <input
                          type="text"
                      className={`mt-1 w-full p-2 rounded text-sm ${themeClasses.inputField(darkMode)} ${themeClasses.inputFocusRing(darkMode)}`}
                          value={newBudgetRule.tags || ''}
                          onChange={handleRuleInputChange('tags')}
                          placeholder="Enter tags"
                        />
                      </div>

                 
                      <div className="mt-4">
                    <label className={`block text-sm font-medium ${themeClasses.checkboxLabel(darkMode)}`}>Link to Goal</label>
                        <input
                          type="text"
                      className={`mt-1 w-full p-2 rounded text-sm ${themeClasses.inputField(darkMode)} ${themeClasses.inputFocusRing(darkMode)}`}
                          value={newBudgetRule.goal || ''}
                          onChange={handleRuleInputChange('goal')}
                          placeholder="Enter goal"
                        />
                      </div>
                    </div>
                  </div>
                </div>
            <div className={`flex justify-center items-center gap-4 p-4 border-t ${themeClasses.modalFooter(darkMode)}`}>
                 
                  <button
                className={`px-4 py-2 rounded-full transition ${themeClasses.reconcileButton(darkMode)}`}
                    onClick={handleSaveRule}
                    disabled={loading}
                  >
                    {editingRule ? 'Update Rule' : 'Save'}
                  </button>
                  
                </div>
              </div>
            </div>
          )}
       
      </div>
    </>
  );
};

export default BudgetRulePage;
*/

import React, { useEffect, useRef, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { FaPlus, FaEdit, FaTrash, FaInfoCircle } from 'react-icons/fa';
import { getCurrentUserId } from '../../utils/AuthUtil';
import {
  toggleCreateRuleModal,
  updateNewRule,
  resetNewRule,
  setLoading,
  clearMessages,
  createBudgetRule,
  setEditingRule,
} from '../../../../logic/redux/budgetRulesSlice';
import { themeClasses } from '../../utils/tailwindUtils';

const BudgetRulePage = ({darkMode}) => {
  const dispatch = useDispatch();
  const {
    budgetRules,
    isCreateRuleModalOpen,
    subcategories,
    newBudgetRule,
    loading,
    error,
    successMessage,
    editingRule,
    accounts,
  } = useSelector((state) => state.budgetRule);

  const [formErrors, setFormErrors] = useState({
    merchantNamePattern: '',
    thresholdAmount: '',
    fromSubCategoryId: '',
    accountId: '',
    toSubCategoryId: '',
    renamedMerchant: '',
    general: '',
  });

  const [selectedFields, setSelectedFields] = useState({
    merchantNamePattern: false,
    thresholdAmount: false,
    fromSubCategoryId: false,
    renamedMerchant: false,
    cascadeFlag: false,
    accountId: false,
  });

  const [conditionType, setConditionType] = useState('MERCHANT');
  const [tooltip, setTooltip] = useState({ open: false, rule: null, x: 0, y: 0 });
  const previousFocus = useRef(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState({
    open: false,
    ruleId: null,
    isDeleting: false,
  });

  const userId = getCurrentUserId();

  useEffect(() => {
    setLoading(true);
    dispatch({ type: 'budgetRule/fetchSubcategories', payload: userId });
    dispatch({ type: 'budgetRule/fetchBudgetRules', payload: userId });
    dispatch({ type: 'budgetRule/fetchAccounts', payload: userId });
  }, [userId, dispatch]);

  useEffect(() => {
    if (successMessage === 'Rule deleted successfully') {
      const timer = setTimeout(() => {
        setDeleteConfirmation({ open: false, ruleId: null, isDeleting: false });
        dispatch(clearMessages());
      }, 2000);
      return () => clearTimeout(timer);
    }
    if (successMessage && successMessage !== 'Rule deleted successfully' && isCreateRuleModalOpen && !loading && !error) {
      const timer = setTimeout(() => {
        dispatch(resetNewRule());
        dispatch(setEditingRule(null));
        dispatch(clearMessages());
        setFormErrors({
          merchantNamePattern: '',
          thresholdAmount: '',
          fromSubCategoryId: '',
          accountId: '',
          toSubCategoryId: '',
          renamedMerchant: '',
          general: '',
        });
        setSelectedFields({
          merchantNamePattern: false,
          thresholdAmount: false,
          fromSubCategoryId: false,
          renamedMerchant: false,
          cascadeFlag: false,
          accountId: false,
        });
        setConditionType('MERCHANT');
        // APT-176 fix for closing the modal after creating/updating rule
        dispatch(toggleCreateRuleModal(false)); // Close the modal
      }, 2000);
      return () => clearTimeout(timer);
    }
    if (error && deleteConfirmation.isDeleting) {
      setDeleteConfirmation((prev) => ({ ...prev, isDeleting: false }));
    }
  }, [successMessage, error, loading, dispatch, isCreateRuleModalOpen]);

  const handleCloseRuleModal = () => {
    dispatch(toggleCreateRuleModal(false));
    dispatch(resetNewRule());
    dispatch(setEditingRule(null));
    dispatch(clearMessages());
    setFormErrors({
      merchantNamePattern: '',
      thresholdAmount: '',
      fromSubCategoryId: '',
      accountId: '',
      toSubCategoryId: '',
      renamedMerchant: '',
      general: '',
    });
    setSelectedFields({
      merchantNamePattern: false,
      thresholdAmount: false,
      fromSubCategoryId: false,
      renamedMerchant: false,
      cascadeFlag: false,
      accountId: false,
    });
    setConditionType('MERCHANT');
  };

  const handleRuleInputChange = (field) => (event) => {
    const value = field === 'merchantNamePattern' || field === 'renamedMerchant' || field === 'tags' || field === 'goal'
      ? event.target.value
      : Number(event.target.value);
    dispatch(updateNewRule({ [field]: value }));
  };

  const handleCategoryChange = (field) => (e) => {
    if (field === 'fromSubCategoryId') {
      const fromSubCatId = Number(e.target.value);
      const selectedSubcategory = subcategories.find((subcat) => subcat.subCategoryId === fromSubCatId);
      
      dispatch(
        updateNewRule({
          fromSubCategoryId: fromSubCatId,
          fromCategoryId: selectedSubcategory?.categoryId || null,
        })
      );
    }

    if (field === 'toSubCategoryId') {
      const [toSubCatId, type] = e.target.value.split('|');
      const selectedSubcategory = subcategories.find(
        (subcat) =>
          String(subcat.subCategoryId) === toSubCatId &&
          String(subcat.type) === type
      );
      
      if (selectedSubcategory) {
        const updateData = {
          toCategoryId: selectedSubcategory.categoryId,
          toSubCategoryId: type === 'standard' ? Number(toSubCatId) : null,
          toCustomSubCategoryId: type === 'custom' ? Number(toSubCatId) : null,
        };
        dispatch(updateNewRule(updateData));
        
        if (!toSubCatId) {
          setFormErrors((prev) => ({ ...prev, toSubCategoryId: 'Target subcategory is required' }));
        } else {
          setFormErrors((prev) => ({ ...prev, toSubCategoryId: '' }));
        }
      }
    }

    if (field === 'fromSubCategoryId' || field === 'toSubCategoryId') {
      validateCategoryMatch();
    }
  };

  const handleFieldToggle = (field) => {
    setSelectedFields((prev) => {
      const newSelected = { ...prev, [field]: !prev[field] };
      if (!newSelected[field]) {
        if (field === 'thresholdAmount') {
          dispatch(updateNewRule({ thresholdAmount: 0, amountType: null, amountMatch: null }));
        } else if (field === 'renamedMerchant') {
          dispatch(updateNewRule({ renamedMerchant: '' }));
        } else if (field === 'cascadeFlag') {
          dispatch(updateNewRule({ cascadeFlag: false }));
        } else if (field === 'merchantNamePattern') {
          dispatch(updateNewRule({ merchantNamePattern: null, merchantMatchRegex: false }));
        }
        setFormErrors((prev) => ({ ...prev, [field]: '' }));
      } else if (field === 'thresholdAmount') {
        dispatch(updateNewRule({
          amountType: newBudgetRule.amountType || 'credit',
          amountMatch: newBudgetRule.amountMatch || 'greater',
        }));
      }
      return newSelected;
    });
  };

  const validateCategoryMatch = () => {
    const fromCategoryId = newBudgetRule.fromSubCategoryId
      ? subcategories.find((subcat) => subcat.subCategoryId === newBudgetRule.fromSubCategoryId)?.categoryId
      : null;
    const toCategoryId = newBudgetRule.toCategoryId;

    if (selectedFields.fromSubCategoryId && fromCategoryId && toCategoryId && fromCategoryId !== toCategoryId) {
      setFormErrors((prev) => ({
        ...prev,
        fromSubCategoryId: 'Source category ID must match target category ID',
      }));
    } else {
      setFormErrors((prev) => ({ ...prev, fromSubCategoryId: '' }));
    }
  };

  const handleSaveRule = () => {
    setFormErrors({
      merchantNamePattern: '',
      thresholdAmount: '',
      fromSubCategoryId: '',
      accountId: '',
      toSubCategoryId: '',
      renamedMerchant: '',
      general: '',
    });

    let hasErrors = false;

    if (
      !selectedFields.merchantNamePattern &&
      !selectedFields.thresholdAmount &&
      !selectedFields.fromSubCategoryId &&
      !selectedFields.accountId
    ) {
      setFormErrors((prev) => ({
        ...prev,
        general: 'At least one of Merchants, Amount, Source Category, or Account must be selected',
      }));
      hasErrors = true;
    }

    if (selectedFields.merchantNamePattern && !newBudgetRule.merchantNamePattern?.trim()) {
      setFormErrors((prev) => ({
        ...prev,
        merchantNamePattern: 'Merchant name is required',
      }));
      hasErrors = true;
    }

    if (selectedFields.thresholdAmount && (!newBudgetRule.thresholdAmount || newBudgetRule.thresholdAmount <= 0)) {
      setFormErrors((prev) => ({
        ...prev,
        thresholdAmount: 'Amount is required and must be greater than 0',
      }));
      hasErrors = true;
    }

    if (selectedFields.fromSubCategoryId && !newBudgetRule.fromSubCategoryId) {
      setFormErrors((prev) => ({
        ...prev,
        fromSubCategoryId: 'Source subcategory is required',
      }));
      hasErrors = true;
    }

    if (selectedFields.accountId && !newBudgetRule.accountId) {
      setFormErrors((prev) => ({
        ...prev,
        accountId: 'Account is required',
      }));
      hasErrors = true;
    }

    if (!newBudgetRule.toSubCategoryId && !newBudgetRule.toCustomSubCategoryId) {
      setFormErrors((prev) => ({
        ...prev,
        toSubCategoryId: 'Target subcategory is required',
      }));
      hasErrors = true;
    }

    if (selectedFields.renamedMerchant && !newBudgetRule.renamedMerchant?.trim()) {
      setFormErrors((prev) => ({
        ...prev,
        renamedMerchant: 'Renamed merchant is required',
      }));
      hasErrors = true;
    }

    validateCategoryMatch();
    if (formErrors.fromSubCategoryId) hasErrors = true;

    if (hasErrors) {
      dispatch(setLoading(false));
      return;
    }

    dispatch(setLoading(true));
    const ruleData = {
      ...newBudgetRule,
      userId: userId,
      merchantMatchRegex: selectedFields.merchantNamePattern ? (newBudgetRule.merchantMatchRegex ? true : false) : false,
      merchantNamePattern: selectedFields.merchantNamePattern ? newBudgetRule.merchantNamePattern : null,
      amountType: selectedFields.thresholdAmount ? (newBudgetRule.amountType || 'credit') : null,
      amountMatch: selectedFields.thresholdAmount ? (newBudgetRule.amountMatch || 'greater') : null,
      thresholdAmount: selectedFields.thresholdAmount ? newBudgetRule.thresholdAmount : 0,
      fromSubCategoryId: selectedFields.fromSubCategoryId ? newBudgetRule.fromSubCategoryId : null,
      fromCategoryId: selectedFields.fromSubCategoryId
        ? newBudgetRule.fromCategoryId
        : newBudgetRule.toCategoryId,
      accountId: selectedFields.accountId ? newBudgetRule.accountId : null,
      renamedMerchant: selectedFields.renamedMerchant ? newBudgetRule.renamedMerchant : null,
      cascadeFlag: newBudgetRule.cascadeFlag || false,
      hideTransactionsFlag: newBudgetRule.hideTransactionsFlag || false,
      tags: newBudgetRule.tags || null,
      goal: newBudgetRule.goal || null,
      conditionType,
      isActive: true,
      ruleType: newBudgetRule.ruleType || 'CUSTOM',
    };
    dispatch(createBudgetRule(ruleData));
  };

  const handleDelete = (ruleId) => {
    previousFocus.current = document.activeElement;
    setDeleteConfirmation({ open: true, ruleId, isDeleting: false });
  };

  const handleConfirmDelete = () => {
    setDeleteConfirmation((prev) => ({ ...prev, isDeleting: true }));
    dispatch({ type: 'budgetRule/deleteBudgetRule', payload: deleteConfirmation.ruleId });
  };

  const handleCancelDelete = () => {
    setDeleteConfirmation({ open: false, ruleId: null, isDeleting: false });
    dispatch(clearMessages());
  };

  const handleEdit = (rule) => {
    dispatch(setEditingRule(rule));
    dispatch(toggleCreateRuleModal(true));
    setConditionType(rule.conditionType || 'MERCHANT');
    setSelectedFields({
      merchantNamePattern: !!rule.merchantNamePattern,
      thresholdAmount: rule.thresholdAmount > 0,
      fromSubCategoryId: !!rule.fromSubCategoryId,
      renamedMerchant: !!rule.renamedMerchant,
      cascadeFlag: !!rule.cascadeFlag,
      accountId: !!rule.accountId,
    });
    dispatch(
      updateNewRule({
        id: rule.id,
        userId: rule.userId,
        ruleType: rule.ruleType,
        fromCategoryId: rule.fromCategoryId,
        fromSubCategoryId: rule.fromSubCategoryId,
        toCategoryId: rule.toCategoryId,
        toSubCategoryId: rule.toSubCategoryId,
        toCustomSubCategoryId: rule.toCustomSubCategoryId,
        conditionType: rule.conditionType,
        thresholdAmount: rule.thresholdAmount,
        thresholdPercentage: rule.thresholdPercentage,
        transferDay: rule.transferDay,
        merchantNamePattern: rule.merchantNamePattern,
        isActive: rule.isActive,
        maxTransferAmount: rule.maxTransferAmount,
        minTransferAmount: rule.minTransferAmount,
        maxTransferPercent: rule.maxTransferPercent,
        minTransferPercent: rule.minTransferPercent,
        executionFrequency: rule.executionFrequency,
        renamedMerchant: rule.renamedMerchant || '',
        cascadeFlag: rule.cascadeFlag,
        merchantMatchRegex: rule.merchantMatchRegex || false,
        amountType: rule.amountType,
        amountMatch: rule.amountMatch,
        accountId: rule.accountId,
        hideTransactionsFlag: rule.hideTransactionsFlag,
        tags: rule.tags,
        goal: rule.goal,
      })
    );
  };

  const handleInfoClick = (event, rule) => {
    event.preventDefault();
    const rect = event.currentTarget.getBoundingClientRect();
    const scrollX = window.scrollX;
    const scrollY = window.scrollY;
    const tooltipWidth = 250;
    let x = rect.left + scrollX - tooltipWidth - 10;
    let y = rect.top + scrollY;
    if (x < scrollX) x = rect.left + scrollX + 20;
    if (y < scrollY) y = scrollY;
    setTooltip({ open: true, rule, x, y });
  };

  const handleCloseTooltip = (event) => {
    if ((event.type === 'keydown' && event.key === 'Escape') ||
       (event.type === 'click' && !event.target.closest('.tooltip'))){
      setTooltip({ open: false, rule: null, x: 0, y: 0 });
    }
  };

  useEffect(() => {
    if (tooltip.open) {
      const timeoutId = setTimeout(() => {
        document.addEventListener('click', handleCloseTooltip);
      }, 0);
      return () => {
        clearTimeout(timeoutId);
        document.removeEventListener('click', handleCloseTooltip);
      };
    }
  }, [tooltip.open]);

  return (
    <div className={`p-2 w-full min-h-screen ${themeClasses.pageContainer(darkMode)}`}>
      <div className="mb-6 flex justify-between items-center">
        <h1 className={`text-2xl`}>Budget Rules</h1>
        <button
          className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition ${themeClasses.reconcileButton(darkMode)}`}
          onClick={() => dispatch(toggleCreateRuleModal(true))}
        >
          <FaPlus className="w-2 h-2 mr-2 fill-current" aria-hidden="true" /> Create Rule
        </button>
      </div>
      <div className={`mt-6 shadow rounded-lg overflow-hidden ${themeClasses.tableContainer(darkMode)}`}>
        <table className="min-w-full">
          <thead className={`${themeClasses.tableHeaderRow(darkMode)}`}>
            <tr>
              <th className={`px-4 py-3 text-left font-bold text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Rule Name</th>
              <th className={`px-4 py-3 text-left font-bold text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Merchant</th>
              <th className={`px-4 py-3 text-left font-bold text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Amount</th>
              <th className={`px-4 py-3 text-left font-bold text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Account ID</th>
              <th className={`px-4 py-3 text-left font-bold text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>From Category</th>
              <th className={`px-4 py-3 text-left font-bold text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>To Category</th>
              <th className={`px-4 py-3 text-left font-bold text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Actions</th>
            </tr>
          </thead>
          <tbody className={`${themeClasses.tableBody(darkMode)}`}>
            {loading ? (
              <tr>
                <td colSpan={6} className="text-center py-4">Loading...</td>
              </tr>
            ) : budgetRules.length === 0 ? (
              <tr>
                <td colSpan={6} className="text-center py-4">No budget rules found</td>
              </tr>
            ) : (
              budgetRules.map((rule) => (
                <tr key={rule.id} className={`border-t ${themeClasses.tableRow(darkMode)}`}>
                  <td className={`px-4 py-2 ${themeClasses.tableCell(darkMode)}`}>{rule.ruleType}</td>
                  <td className={`px-4 py-2 ${themeClasses.tableCell(darkMode)}`}>{rule.merchantNamePattern || '-'}</td>
                  <td className={`px-4 py-2 ${themeClasses.tableCell(darkMode)}`}>{rule.thresholdAmount || '-'}</td>
                  <td className={`px-4 py-2 ${themeClasses.tableCell(darkMode)}`}>{rule.accountName || '-'}</td>
                  <td className={`px-4 py-2 ${themeClasses.tableCell(darkMode)}`}>{rule.fromCatName || '-'}</td>
                  <td className={`px-4 py-2 ${themeClasses.tableCell(darkMode)}`}>{rule.toCatName || '-'}</td>
                  <td className="px-2 py-1 flex space-x-3 items-center">
                    <button
                      className="!text-gray-600 hover:text-gray-800 p-2 rounded-full hover:bg-gray-100 transition !visible !opacity-100"
                      onClick={(e) => handleInfoClick(e, rule)}
                      title="View Rule Details"
                      aria-label={`View details for rule ${rule.ruleType}`}
                    >
                      <FaInfoCircle className="w-4 h-4 fill-current" aria-hidden="true" />
                    </button>
                    <button
                      className="!text-blue-600 hover:text-blue-800 p-2 rounded-full hover:bg-blue-100 transition !visible !opacity-100"
                      onClick={() => handleEdit(rule)}
                      title="Edit Rule"
                      aria-label={`Edit rule ${rule.ruleType}`}
                    >
                      <FaEdit className="w-4 h-4 fill-current" aria-hidden="true" />
                    </button>
                    <button
                      className="!text-red-600 hover:text-red-800 p-2 rounded-full hover:bg-red-100 transition !visible !opacity-100"
                      onClick={() => handleDelete(rule.id)}
                      title="Delete Rule"
                      aria-label={`Delete rule ${rule.ruleType}`}
                      tabIndex={deleteConfirmation.open ? -1 : 0}
                    >
                      <FaTrash className="w-4 h-4 fill-current" aria-hidden="true" />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {tooltip.open && tooltip.rule && (
        <div
          className="tooltip"
          style={{ left: tooltip.x, top: tooltip.y }}
          onClick={(e) => e.stopPropagation()}
          onKeyDown={handleCloseTooltip}
          tabIndex={0}
        >
          <table className="tooltip-table">
            <tbody>
              <tr><td>Rename Flag</td><td>{tooltip.rule.renamedMerchant ? 'Yes' : 'No'}</td></tr>
              <tr><td>Rename Merchant To</td><td>{tooltip.rule.renamedMerchant || '-'}</td></tr>
              <tr><td>Cascade Flag</td><td>{tooltip.rule.cascadeFlag ? 'Yes' : 'No'}</td></tr>
              <tr><td>Hide Transactions</td><td>{tooltip.rule.hideTransactionsFlag ? 'Yes' : 'No'}</td></tr>
              <tr><td>Tags</td><td>{tooltip.rule.tags || '-'}</td></tr>
              <tr><td>Goal</td><td>{tooltip.rule.goal || '-'}</td></tr>
            </tbody>
          </table>
        </div>
      )}

      {deleteConfirmation.open && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-sm mx-4">
            <div className="p-6">
              {deleteConfirmation.isDeleting ? (
                successMessage === 'Rule deleted successfully' ? (
                  <div className="flex flex-col items-center gap-4">
                    <p className="text-green-700">Rule deleted successfully</p>
                  </div>
                ) : (
                  <div className="flex flex-col items-center gap-4" role="status">
                    <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <p>Deleting budget rule...</p>
                  </div>
                )
              ) : (
                <>
                  <p className="text-gray-700">Are you sure you want to delete this budget rule?</p>
                  <p className="text-sm text-gray-500 mt-2">This action cannot be undone.</p>
                </>
              )}
              {error && (
                <div className="mt-4 p-3 bg-red-100 text-red-700 rounded flex justify-between items-center">
                  {error}
                  <button onClick={() => dispatch(clearMessages())} className="text-red-700 hover:text-red-900">
                    ×
                  </button>
                </div>
              )}
            </div>
            <div className="flex justify-center gap-4 p-4 border-t">
              {!deleteConfirmation.isDeleting && (
                <>
                  <button
                    onClick={handleCancelDelete}
                    className="px-4 py-2 border border-gray-300 rounded text-black hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    tabIndex={deleteConfirmation.open ? 0 : -1}
                    style={{ display: 'block', visibility: 'visible', opacity: 1 }}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleConfirmDelete}
                    className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                    tabIndex={deleteConfirmation.open ? 0 : -1}
                    style={{ display: 'block', visibility: 'visible', opacity: 1 }}
                  >
                    Delete
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {isCreateRuleModalOpen && (
        <div className={`fixed inset-0 flex items-center justify-center z-50 ${themeClasses.modalOverlay(darkMode)}`}>
          <div className={`rounded-lg w-full max-w-4xl mx-4 h-[90vh] flex flex-col ${themeClasses.modalContent(darkMode)}`}>
            <div className={`border-b p-4 text-center font-medium relative ${themeClasses.modalHeader(darkMode)}`}>
              {editingRule ? 'Edit Budget Rule' : 'Create Budget Rule'}
              <button
                className={`absolute right-4 top-4 ${themeClasses.closeButton(darkMode)}`}
                onClick={handleCloseRuleModal}
                disabled={loading}
                aria-label="Close modal"
              >
                <svg className={`w-6 h-6 ${themeClasses.icon(darkMode)}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="flex flex-1 overflow-hidden">
              <div className={`w-1/2 p-4 overflow-y-auto scrollbar-hidden ${themeClasses.modalPanelLeft(darkMode)}`}>
                <h3 className="text-md font-semibold mb-4">Conditions</h3>
                <div className="flex flex-col gap-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className={`h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
                      checked={selectedFields.merchantNamePattern}
                      onChange={() => {
                        handleFieldToggle('merchantNamePattern');
                        if (!selectedFields.merchantNamePattern) {
                          setFormErrors((prev) => ({ ...prev, merchantNamePattern: '' }));
                        }
                      }}
                    />
                    <span className={`ml-2 text-sm font-medium ${themeClasses.checkboxLabel(darkMode)}`}>Merchant</span>
                  </label>
                  {selectedFields.merchantNamePattern && (
                    <>
                      <div className="ml-6 mt-2 flex gap-2">
                        <select
                          className={`w-1/3 p-2 rounded text-sm ${themeClasses.selectField(darkMode)} ${themeClasses.inputFocusRing(darkMode)}`}
                          value={newBudgetRule.merchantMatchRegex ? 'contains' : 'equals'}
                          onChange={(e) => dispatch(updateNewRule({ merchantMatchRegex: e.target.value === 'contains' }))}
                        >
                          <option value="contains">Contains</option>
                          <option value="equals">Exact Match</option>
                        </select>
                        <input
                          type="text"
                          className={`w-2/3 p-2 border rounded text-sm ${themeClasses.inputField(darkMode)} ${themeClasses.inputFocusRing(darkMode)} ${formErrors.merchantNamePattern ? 'border-red-500' : ''}`}
                          value={newBudgetRule.merchantNamePattern || ''}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (!value.trim()) {
                              setFormErrors((prev) => ({ ...prev, merchantNamePattern: 'Merchant name is required' }));
                            } else {
                              setFormErrors((prev) => ({ ...prev, merchantNamePattern: '' }));
                            }
                            handleRuleInputChange('merchantNamePattern')(e);
                          }}
                          placeholder="Enter merchant name"
                        />
                      </div>
                      <p className={`ml-6 text-xs mt-1 ${formErrors.merchantNamePattern ? themeClasses.errorText(darkMode) : themeClasses.noChartsText(darkMode)}`}>
                        {formErrors.merchantNamePattern || ''}
                      </p>
                    </>
                  )}

                  <label className="flex items-center mt-2">
                    <input
                      type="checkbox"
                      className={`h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
                      checked={selectedFields.thresholdAmount}
                      onChange={() => {
                        handleFieldToggle('thresholdAmount');
                        if (!selectedFields.thresholdAmount) {
                          setFormErrors((prev) => ({ ...prev, thresholdAmount: '' }));
                        }
                      }}
                    />
                    <span className={`ml-2 text-sm font-medium ${themeClasses.checkboxLabel(darkMode)}`}>Amount</span>
                  </label>
                  {selectedFields.thresholdAmount && (
                    <>
                      <div className="ml-6 mt-2 flex gap-2">
                        <select
                          className={`w-1/3 p-2 rounded text-sm ${themeClasses.selectField(darkMode)} ${themeClasses.inputFocusRing(darkMode)}`}
                          value={newBudgetRule.amountType || 'credit'}
                          onChange={(e) => dispatch(updateNewRule({ amountType: e.target.value }))}
                        >
                          <option value="credit">Credit</option>
                          <option value="debit">Debit</option>
                        </select>
                        <select
                          className={`w-1/3 p-2 rounded text-sm ${themeClasses.selectField(darkMode)} ${themeClasses.inputFocusRing(darkMode)}`}
                          value={newBudgetRule.amountMatch || 'greater'}
                          onChange={(e) => dispatch(updateNewRule({ amountMatch: e.target.value }))}
                        >
                          <option value="equals">Equals</option>
                          <option value="greater">Greater than</option>
                          <option value="less">Less than</option>
                        </select>
                        <input
                          type="number"
                          className={`w-1/3 p-2 rounded text-sm ${themeClasses.inputField(darkMode)} ${themeClasses.inputFocusRing(darkMode)} ${formErrors.thresholdAmount ? 'border-red-500' : ''}`}
                          value={newBudgetRule.thresholdAmount || ''}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (!value || value <= 0) {
                              setFormErrors((prev) => ({ ...prev, thresholdAmount: 'Amount is required and must be greater than 0' }));
                            } else {
                              setFormErrors((prev) => ({ ...prev, thresholdAmount: '' }));
                            }
                            handleRuleInputChange('thresholdAmount')(e);
                          }}
                          placeholder="Amount"
                        />
                      </div>
                      <p className={`ml-6 text-xs mt-1 ${formErrors.thresholdAmount ? 'text-red-500' : 'text-gray-500'}`}>
                        {formErrors.thresholdAmount || 'Enter amount threshold'}
                      </p>
                    </>
                  )}

                  <label className="flex items-center mt-4">
                    <input
                      type="checkbox"
                      className={`h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
                      checked={selectedFields.fromSubCategoryId}
                      onChange={() => {
                        handleFieldToggle('fromSubCategoryId');
                        if (!selectedFields.fromSubCategoryId) {
                          setFormErrors((prev) => ({ ...prev, fromSubCategoryId: '' }));
                        }
                      }}
                    />
                    <span className={`ml-2 text-sm font-medium ${themeClasses.checkboxLabel(darkMode)}`}>Source Subcategory</span>
                  </label>
                  {selectedFields.fromSubCategoryId && (
                    <div className="ml-6 mt-2">
                      <select
                        className={`w-full p-2 rounded text-sm ${themeClasses.selectField(darkMode)} ${themeClasses.inputFocusRing(darkMode)} ${formErrors.fromSubCategoryId ? 'border-red-500' : ''}`}
                        value={newBudgetRule.fromSubCategoryId || ''}
                        onChange={(e) => {
                          const fromSubCatId = Number(e.target.value);
                          if (!fromSubCatId) {
                            setFormErrors((prev) => ({ ...prev, fromSubCategoryId: 'Source subcategory is required' }));
                          } else {
                            handleCategoryChange('fromSubCategoryId')(e);
                          }
                        }}
                      >
                        <option value="" disabled>Select source subcategory</option>
                        {subcategories && subcategories.length > 0 ? (
                          subcategories.filter((subcat) => subcat.type === 'standard').map((subcat) => (
                            <option key={`${subcat.subCategoryId}-${subcat.type}`} value={subcat.subCategoryId}>
                              {subcat.subCategoryName}
                            </option>
                          ))
                        ) : (
                          <option value="" disabled>No subcategories available</option>
                        )}
                      </select>
                      <p className={`text-xs mt-1 ${formErrors.fromSubCategoryId ? themeClasses.errorText(darkMode) : themeClasses.noChartsText(darkMode)}`}>
                        {formErrors.fromSubCategoryId || ""}
                      </p>
                    </div>
                  )}

                  <label className="flex items-center mt-4">
                    <input
                      type="checkbox"
                      className={`h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
                      checked={selectedFields.accountId}
                      onChange={() => handleFieldToggle('accountId')}
                    />
                    <span className={`ml-2 text-sm font-medium ${themeClasses.checkboxLabel(darkMode)}`}>Account</span>
                  </label>
                  {selectedFields.accountId && (
                    <div className="ml-6 mt-2">
                      {loading ? (
                        <div className={`${themeClasses.loadingText(darkMode)}`}>Loading accounts...</div>
                      ) : (
                        <select
                          className={`w-full p-2 rounded text-sm ${themeClasses.selectField(darkMode)} ${themeClasses.inputFocusRing(darkMode)} ${formErrors.accountId ? 'border-red-500' : ''}`}
                          value={newBudgetRule.accountId || ''}
                          onChange={(e) => {
                            const accountId = Number(e.target.value);
                            dispatch(updateNewRule({ accountId }));
                            if (!accountId) {
                              setFormErrors((prev) => ({ ...prev, accountId: 'Account is required' }));
                            } else {
                              setFormErrors((prev) => ({ ...prev, accountId: '' }));
                            }
                          }}
                        >
                          <option value="">Select account</option>
                          {Array.isArray(accounts) && accounts.length > 0 ? (
                            accounts.map((account) => (
                              <option key={account.accountId} value={account.accountId}>
                                {account.accountName}
                              </option>
                            ))
                          ) : (
                            <option value="" disabled>No accounts available</option>
                          )}
                        </select>
                      )}
                      {formErrors.accountId && 
                        <p className={`text-xs mt-1 ${themeClasses.errorText(darkMode)}`}>{formErrors.accountId}</p>
                      }
                    </div>
                  )}

                  {(!selectedFields.merchantNamePattern &&
                    !selectedFields.thresholdAmount &&
                    !selectedFields.fromSubCategoryId &&
                    !selectedFields.accountId) && (
                    <p className={`ml-6 text-xs mt-2 ${themeClasses.errorText(darkMode)}`}>
                      At least one of Merchants, Amount, Source Category, or Account must be selected
                    </p>
                  )}
                  <div className="mt-60 ml-1">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className={`h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
                        checked={newBudgetRule.cascadeFlag || false}
                        onChange={(e) => dispatch(updateNewRule({ cascadeFlag: e.target.checked }))}
                      />
                      <span className={`ml-2 text-sm ${themeClasses.checkboxLabel(darkMode)}`}>Run rules for past transactions</span>
                    </label>
                  </div>
                </div>
              </div>

              <div className={`w-1/2 p-4 overflow-y-auto relative ${themeClasses.modalPanelRight(darkMode)}`}>
                <h3 className="text-m font-bold">Apply</h3>
                {/* {loading && (
                  <div className={`absolute inset-0 bg-opacity-50 flex items-center justify-center z-10 ${themeClasses.loadingOverlay(darkMode)}`}>
                    <div className="flex items-center">
                      <div className={`w-8 h-8 border-4 rounded-full animate-spin mr-2 ${themeClasses.spinner(darkMode)}`} />
                      <span className={`${themeClasses.loadingText(darkMode)}`}>Saving...</span>
                    </div>
                  </div>
                )} */}
                {error && successMessage !== 'Rule deleted successfully' && (
                  <div className={`mb-4 p-3 rounded flex justify-between items-center ${themeClasses.error(darkMode)}`}>
                    {error}
                    <button onClick={() => dispatch(clearMessages())} className={`${themeClasses.errorText(darkMode)} hover:text-red-900`}>
                      ×
                    </button>
                  </div>
                )}
                {successMessage && successMessage !== 'Rule deleted successfully' && (
                  <div className={`mb-4 p-3 rounded flex justify-between items-center ${themeClasses.saveButtonSaved(darkMode)}`}>
                    {successMessage}
                    <button onClick={() => dispatch(clearMessages())} className={`${themeClasses.successText(darkMode)} hover:text-green-900`}>
                      ×
                    </button>
                  </div>
                )}
                <div className="flex flex-col gap-3 relative z-20 mt-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className={`h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
                      checked={selectedFields.renamedMerchant}
                      onChange={() => {
                        handleFieldToggle('renamedMerchant');
                        if (!selectedFields.renamedMerchant) {
                          setFormErrors((prev) => ({ ...prev, renamedMerchant: '' }));
                        }
                      }}
                    />
                    <span className={`ml-2 text-sm ${themeClasses.checkboxLabel(darkMode)}`}>Rename Merchant</span>
                  </label>
                  {selectedFields.renamedMerchant && (
                    <div>
                      <label className={`block text-sm font-medium ${themeClasses.checkboxLabel(darkMode)}`}>Renamed Merchant *</label>
                      <input
                        type="text"
                        className={`mt-1 w-full p-2 rounded text-sm ${themeClasses.inputField(darkMode)} ${themeClasses.inputFocusRing(darkMode)} ${formErrors.renamedMerchant ? 'border-red-500' : ''}`}
                        value={newBudgetRule.renamedMerchant || ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          if (!value.trim()) {
                            setFormErrors((prev) => ({ ...prev, renamedMerchant: 'Renamed merchant is required' }));
                          } else {
                            setFormErrors((prev) => ({ ...prev, renamedMerchant: '' }));
                          }
                          handleRuleInputChange('renamedMerchant')(e);
                        }}
                        placeholder="Enter renamed merchant"
                      />
                      <p className={`text-xs mt-1 ${formErrors.renamedMerchant ? themeClasses.errorText(darkMode) : themeClasses.noChartsText(darkMode)}`}>
                        {formErrors.renamedMerchant || 'Enter renamed merchant to be saved'}
                      </p>
                    </div>
                  )}

                  <div>
                    <label className={`block text-sm font-medium ${themeClasses.checkboxLabel(darkMode)}`}>Target Subcategory *</label>
                    <select
                      className={`mt-1 w-full p-2 rounded text-sm ${themeClasses.selectField(darkMode)} ${themeClasses.inputFocusRing(darkMode)} ${formErrors.toSubCategoryId ? 'border-red-500' : ''}`}
                      value={`${newBudgetRule.toCustomSubCategoryId || newBudgetRule.toSubCategoryId || ''}|${newBudgetRule.toCustomSubCategoryId ? 'custom' : 'standard'}`}
                      onChange={handleCategoryChange('toSubCategoryId')}
                      required
                    >
                      <option value="" disabled>Select target subcategory</option>
                      {subcategories && subcategories.length > 0 ? (
                        subcategories.map((subcat) => (
                          <option key={`${subcat.subCategoryId}-${subcat.type}`} value={`${subcat.subCategoryId}|${subcat.type}`}>
                            {subcat.subCategoryName} ({subcat.type})
                          </option>
                        ))
                      ) : (
                        <option value="" disabled>No subcategories available</option>
                      )}
                    </select>
                    <p className={`text-xs mt-1 ${formErrors.toSubCategoryId ? themeClasses.errorText(darkMode) : themeClasses.noChartsText(darkMode)}`}>
                      {formErrors.toSubCategoryId || ''}
                    </p>
                  </div>

                  <label className="flex items-center mt-4">
                    <input
                      type="checkbox"
                      className={`h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
                      checked={newBudgetRule.hideTransactionsFlag || false}
                      onChange={(e) => dispatch(updateNewRule({ hideTransactionsFlag: e.target.checked }))}
                    />
                    <span className={`ml-2 text-sm ${themeClasses.checkboxLabel(darkMode)}`}>Hide transactions</span>
                  </label>

                  <div className="mt-4">
                    <label className={`block text-sm font-medium ${themeClasses.checkboxLabel(darkMode)}`}>Add Tags</label>
                    <input
                      type="text"
                      className={`mt-1 w-full p-2 rounded text-sm ${themeClasses.inputField(darkMode)} ${themeClasses.inputFocusRing(darkMode)}`}
                      value={newBudgetRule.tags || ''}
                      onChange={handleRuleInputChange('tags')}
                      placeholder="Enter tags"
                    />
                  </div>

                  <div className="mt-4">
                    <label className={`block text-sm font-medium ${themeClasses.checkboxLabel(darkMode)}`}>Link to Goal</label>
                    <input
                      type="text"
                      className={`mt-1 w-full p-2 rounded text-sm ${themeClasses.inputField(darkMode)} ${themeClasses.inputFocusRing(darkMode)}`}
                      value={newBudgetRule.goal || ''}
                      onChange={handleRuleInputChange('goal')}
                      placeholder="Enter goal"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className={`flex justify-center items-center gap-4 p-4 border-t ${themeClasses.modalFooter(darkMode)}`}>
              {/* <button
                className={`px-4 py-2 rounded-full transition ${themeClasses.reconcileButton(darkMode)}`}
                onClick={handleSaveRule}
                disabled={loading}
              >
                {editingRule ? 'Update Rule' : 'Save'}
              </button> */}
              <button
                className={`px-4 py-2 rounded-full flex items-center gap-2 transition ${themeClasses.reconcileButton(darkMode)}`}
                onClick={handleSaveRule}
                disabled={loading}
              >
                {loading && (
                  <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                )}
                {loading ? (editingRule ? 'Updating...' : 'Saving...') : (editingRule ? 'Update Rule' : 'Save Rule')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BudgetRulePage;