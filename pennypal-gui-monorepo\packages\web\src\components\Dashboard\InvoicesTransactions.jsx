import React, { useEffect, useState } from 'react';
import { axiosInstance } from 'logic/api/axiosConfig'; // Adjust path as needed
import { getCurrentUserId } from '../../utils/AuthUtil'; // Adjust path as needed
import { FiChevronRight, FiChevronDown } from 'react-icons/fi';
import PaymentLoader from '../load/PaymentLoader'; // Adjust path as needed
import { themeClasses } from '../../utils/tailwindUtils'; // Adjust path as needed

const InvoicesTransactions = ({ darkMode }) => {
  const userId = getCurrentUserId();
  const [subscriptionInfo, setSubscriptionInfo] = useState(null);
  const [invoices, setInvoices] = useState([]);
  const [invoiceUpcoming, setInvoiceUpcoming] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('invoices'); // Default to 'invoices' tab

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch subscription
        try {
          const subscriptionRes = await axiosInstance.get(`/pennypal/api/v1/payment/subscription?userId=${userId}`);
          const subscriptionData = subscriptionRes.data;
          setSubscriptionInfo(subscriptionData || null);
        } catch (error) {
          throw new Error('Failed to fetch subscription');
        }

        // Fetch invoices
        try {
          const invoicesRes = await axiosInstance.get(`/pennypal/api/v1/payment/invoices?userId=${userId}`);
          const invoicesData = invoicesRes.data;
          setInvoices(invoicesData || []);
        } catch (error) {
          throw new Error('Failed to fetch invoices');
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchData();
    }
  }, [userId]);

  useEffect(() => {
    if (subscriptionInfo?.customerId && subscriptionInfo?.subscriptionId) {
      const fetchInvoiceUpcoming = async () => {
        try {
          const invoiceUpcomingRes = await axiosInstance.post(
            '/pennypal/api/v1/payment/invoice/upcoming',
            {
              userId,
              customerId: subscriptionInfo.customerId,
              subscriptionId: subscriptionInfo.subscriptionId,
            }
          );
          const invoiceUpcomingData = invoiceUpcomingRes.data;
          setInvoiceUpcoming(invoiceUpcomingData || null);
        } catch (error) {
          console.error('Failed to fetch upcoming invoice');
          setInvoiceUpcoming(null);
        }
      };

      fetchInvoiceUpcoming();
    }
  }, [subscriptionInfo?.customerId, subscriptionInfo?.subscriptionId]);

  if (loading) {
    return (
      <div className={`min-h-screen w-full p-5 flex flex-col justify-center items-center ${themeClasses.container(darkMode)}`}>
        <PaymentLoader darkMode={darkMode} />
        {/* <p className={`mt-2 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Loading invoices...
        </p> */}
      </div>
    );
  }

  if (error) {
    return (
      <div className={`w-full max-w-4xl mx-auto p-4 ${themeClasses.container(darkMode)}`}>
        <div className={`border px-4 py-3 rounded relative ${themeClasses.error(darkMode)}`} role="alert">
          <strong className={`font-bold ${darkMode ? 'text-red-400' : 'text-red-700'}`}>Error:</strong>
          <span className={`block sm:inline ${darkMode ? 'text-red-300' : 'text-red-700'}`}> {error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-2 p-4 ${themeClasses.container(darkMode)}`}>
      <div className={`flex mb-4 border-b ${themeClasses.border(darkMode)}`}>
        {/* Invoices Paid Tab */}
        <button
          className={`flex items-center space-x-2 px-4 py-2 rounded-t ${
            activeTab === 'invoices' ? themeClasses.tabActive(darkMode) : themeClasses.tabInactive(darkMode)
          }`}
          onClick={() => setActiveTab('invoices')}
        >
          <span>Invoices Paid</span>
        </button>

        {/* Upcoming Invoice Tab */}
        <button
          className={`flex items-center space-x-2 px-4 py-2 rounded-t ${
            activeTab === 'upcoming' ? themeClasses.tabActive(darkMode) : themeClasses.tabInactive(darkMode)
          }`}
          onClick={() => setActiveTab('upcoming')}
        >
          <span>Upcoming Invoice</span>
        </button>
      </div>

      <div className="mt-4">
        {activeTab === 'invoices' && (
          <div>
            {invoices.length === 0 ? (
              <p>No paid invoices available.</p>
            ) : (
              <table className={`min-w-full rounded-t-xl rounded-b-xl overflow-hidden shadow-lg max-w-lg w-full text-sm mb-2 ${themeClasses.border(darkMode)}`}>
                <thead>
                  <tr className={`text-left border-b ${themeClasses.tableHeader(darkMode)} ${themeClasses.border(darkMode)}`}>
                    <th className="py-2 px-2">Date</th>
                    <th className="py-2 px-2">Amount Paid</th>
                    <th className="py-2 px-2">Reason</th>
                    <th className="py-2 px-2">Invoice</th>
                    <th className="py-2 px-2">PDF</th>
                  </tr>
                </thead>
                <tbody>
                  {invoices.map((invoice) => (
                    <tr
                      key={invoice.invoiceId}
                      className={`border-b ${themeClasses.border(darkMode)} ${themeClasses.container(darkMode)}`}
                    >
                      <td className="py-1 px-2">{invoice.createdAt.substring(0, 10)}</td>
                      <td className="py-1 px-2">$ {Number(invoice.amountPaid).toFixed(2)}</td>
                      <td className="py-1 px-2">{invoice.billingReason.replace('_', ' ')}</td>
                      <td className="py-1 px-2">
                        <a
                          href={invoice.invoiceUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className={`${themeClasses.link(darkMode)} underline`}
                        >
                          Invoice Link
                        </a>
                      </td>
                      <td className="py-1 px-2">
                        <a
                          href={invoice.invoicePdf}
                          target="_blank"
                          rel="noopener noreferrer"
                          title="Download PDF"
                          className={themeClasses.linkSecondary(darkMode)}
                        >
                          🧾
                        </a>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        )}

        {activeTab === 'upcoming' && (
          <div className="transition-all duration-300 ease-in-out">
            {invoiceUpcoming == null ? (
              <p>No upcoming invoice</p>
            ) : (
              <table className={`w-full text-sm mb-2 ${themeClasses.border(darkMode)}`}>
                <thead>
                  <tr className={`text-left border-b  ${themeClasses.border(darkMode)}`}>
                    <th className="py-1">Description</th>
                    <th className="py-1 text-right">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {invoiceUpcoming.items.map((item, idx) => (
                    <tr
                      key={idx}
                      className={`border-b ${themeClasses.border(darkMode)} ${themeClasses.container(darkMode)}`}
                    >
                      <td className="py-1">{item.description}</td>
                      <td
                        className={`py-1 text-right ${
                          item.credit ? themeClasses.amountCredit(darkMode) : themeClasses.amountDebit(darkMode)
                        }`}
                      >
                        {item.credit ? '-' : '+'}$ {Math.abs(parseFloat(item.amount)).toFixed(2)}
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr className={`font-bold ${themeClasses.container(darkMode)}`}>
                    <td className="py-1">
                      Total Due on {invoiceUpcoming.nextPaymentDate.substring(0, 10)}
                    </td>
                    <td className="py-1 text-right">
                      $ {parseFloat(invoiceUpcoming.totalAmount).toFixed(2)} {invoiceUpcoming.currency}
                    </td>
                  </tr>
                </tfoot>
              </table>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default InvoicesTransactions;