import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { fetchTransactionsStart } from '../../../../logic/redux/transactionSlice';

const RecentTransactions = ({ darkMode }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  // Get allTransactions from Redux store
  const { allTransactions, loading, error } = useSelector((state) => state.transactions);
  
  useEffect(() => {
    // Fetch transactions if they haven't been loaded yet
    if (allTransactions.length === 0 && !loading) {
      dispatch(fetchTransactionsStart());
    }
  }, [dispatch, allTransactions.length, loading]);

  // Get the 10 most recent transactions
  const recentTransactions = React.useMemo(() => {
    if (!allTransactions || allTransactions.length === 0) return [];
    
    // Sort transactions by date (newest first)
    return [...allTransactions]
      .sort((a, b) => {
        const dateA = new Date(a.transactionDate);
        const dateB = new Date(b.transactionDate);
        return dateB - dateA; // Sort in descending order (newest first)
      })
      .slice(0, 10); // Take only the first 10
  }, [allTransactions]);

  // Format date from ISO string to MM/DD
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return `${date.getMonth() + 1}/${date.getDate()}`;
  };

  // Handle click on the component to navigate to transactions page
  const handleComponentClick = () => {
    navigate('/dashboard/transactions');
  };

  if (loading) {
    return <div className="h-full flex items-center justify-center">Loading transactions...</div>;
  }

  if (error) {
    return <div className="h-full flex items-center justify-center text-red-500">Error loading transactions</div>;
  }

  return (
    <div className={`h-full flex flex-col ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
      <div className="flex justify-between items-center mb-4">
        <div className={`text-xl font-roboto ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>Recent Transactions</div>
        <button 
          onClick={handleComponentClick}
          className={`text-sm ${darkMode ? 'text-blue-400 hover:text-blue-300' : 'text-blue-500 hover:text-blue-700'} focus:outline-none`}
        >
          View all
        </button>
      </div>
      {recentTransactions.length === 0 ? (
        <div className={`text-center py-8 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>No transactions available</div>
      ) : (
        <div className="overflow-auto flex-grow">
          <table className="min-w-full">
            <thead className={`${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
              <tr>
                <th className={`py-2 px-3 text-left text-xs font-medium uppercase tracking-wider ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Date</th>
                <th className={`py-2 px-3 text-left text-xs font-medium uppercase tracking-wider ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Description</th>
                <th className={`py-2 px-3 text-right text-xs font-medium uppercase tracking-wider ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Amount</th>
              </tr>
            </thead>
            <tbody className={`divide-y ${darkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
              {recentTransactions.map((tx) => (
                <tr 
                  key={tx.transactionId || tx.id}
                  onClick={handleComponentClick}
                  className={`cursor-pointer ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-50'}`}
                >
                  <td className={`py-2 px-3 text-sm ${darkMode ? 'text-gray-100': 'text-gray-800' }`}>
                    {formatDate(tx.transactionDate)}
                  </td>
                  <td className={`py-2 px-3 text-sm ${darkMode ? 'text-gray-100': 'text-gray-800' }`}>
                    {tx.description || tx.category || 'Unnamed Transaction'}
                  </td>
                  <td className={`py-2 px-3 text-sm text-right ${Number(tx.transactionAmount) >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    ${Math.abs(Number(tx.transactionAmount || tx.amount)).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default RecentTransactions;