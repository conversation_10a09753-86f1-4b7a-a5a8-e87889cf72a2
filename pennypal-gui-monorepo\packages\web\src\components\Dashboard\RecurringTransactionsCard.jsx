import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectRecurringTransactions,
  selectLoading,
  selectError,
  fetchRecurringTransactionsStart
} from '../../../../logic/redux/recurringTransactionsSlice';

const RecurringTransactionsCard = ({ darkMode }) => {
  const [activeTab, setActiveTab] = useState('thisMonth');
  const [expanded, setExpanded] = useState(false);
  const dispatch = useDispatch();
  
  const allTransactions = useSelector(selectRecurringTransactions);
  const loading = useSelector(selectLoading);
  const error = useSelector(selectError);
  
  useEffect(() => {
    dispatch(fetchRecurringTransactionsStart());
  }, [dispatch]);
  
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();
  
  const thisMonthTransactions = allTransactions.filter(tx => {
    const txDate = new Date(tx.date);
    return txDate.getMonth() === currentMonth && txDate.getFullYear() === currentYear;
  });
  
  const futureTransactions = allTransactions.filter(tx => {
    const txDate = new Date(tx.date);
    return (txDate.getMonth() > currentMonth && txDate.getFullYear() === currentYear) || 
           (txDate.getFullYear() > currentYear);
  });
  
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };
  
  const renderTransactions = (transactions) => {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#7fe029]"></div>
        </div>
      );
    }
    
    // if (error) {
    //   return (
    //     <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded-md my-4">
    //       <div className="flex">
    //         <div className="flex-shrink-0">
    //           <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
    //             <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
    //           </svg>
    //         </div>
    //         <div className="ml-3">
    //           <p className="text-sm text-red-700">{error}</p>
    //         </div>
    //       </div>
    //     </div>
    //   );
    // }
    
    if (transactions.length === 0) {
      return (
        <div className={`flex flex-col items-center justify-center py-12 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50 '}}`}>
          <svg
            className={`w-16 h-16 ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <p className={`mt-4 text-lg ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>
            No transactions found
          </p>
        </div>
      );
    }
    
    const visibleTransactions = expanded ? transactions : transactions.slice(0, 5);
    
    return (
      <div className={`overflow-hidden ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={`${darkMode ? 'bg-gray-700' : 'bg-[#f0fae6]'} border-b ${darkMode ? 'border-gray-600' : 'border-[#e6f7d9]'}`}>
              <tr>
                <th className={`py-3.5 px-4 text-left text-xs font-semibold ${darkMode ? 'text-gray-100' : 'text-gray-900'} uppercase tracking-wider`}>Merchant</th>
                <th className={`py-3.5 px-4 text-right text-xs font-semibold ${darkMode ? 'text-gray-100' : 'text-gray-900'} uppercase tracking-wider`}>Amount</th>
                <th className={`py-3.5 px-4 text-left text-xs font-semibold ${darkMode ? 'text-gray-100' : 'text-gray-900'} uppercase tracking-wider`}>Date</th>
                {/* <th className="py-3.5 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Category</th>
                <th className="hidden md:table-cell py-3.5 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Frequency</th>
                <th className="hidden md:table-cell py-3.5 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Account</th> */}
              </tr>
            </thead>
            <tbody className={`divide-y ${darkMode ? 'divide-gray-600' : 'divide-[#e6f7d9]'}`}>
              {visibleTransactions.map((tx, index) => (
                <tr key={tx.id + '-' + index} className={`${darkMode ? 'text-gray-100 hover:bg-gray-700' : 'hover:bg-[#f8fcf2] text-black'} transition-colors duration-150`}>
                  <td className="py-4 px-4">
                    <div className="flex items-center">
                      <div className={`${darkMode ? 'text-gray-500 ' : ' text-[#7fe029]'} h-10 w-10 rounded-full bg-[#e6f7d9] flex items-center justify-center mr-3 font-medium `}>
                        {tx.merchant.charAt(0).toUpperCase()}
                      </div>
                      <span className={`${darkMode ? 'text-gray-100' : 'text-gray-900'} font-medium `}>{tx.merchant}</span>
                    </div>
                  </td>
                  <td className="py-4 px-4 text-right whitespace-nowrap">
                    <span className={`font-medium ${tx.amount < 0 ? 'text-red-600' : 'text-[#7fe029]'}`}>
                      {formatCurrency(tx.amount)}
                    </span>
                  </td>
                  <td className={`py-4 px-4 whitespace-nowrap text-sm ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
                    {formatDate(tx.date)}
                  </td>
                  {/* <td className="py-4 px-4">
                    <span className="px-3 py-1.5 text-xs font-medium rounded-full bg-[#e6f7d9] text-[#5cb118]">
                      {tx.category}
                    </span>
                  </td>
                  <td className="hidden md:table-cell py-4 px-4 text-sm text-gray-600">
                    {tx.frequency}
                  </td>
                  <td className="hidden md:table-cell py-4 px-4 text-sm text-gray-600">
                    {tx.account}
                  </td> */}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {transactions.length > 5 && (
 <div className="flex justify-center mt-4 mb-4">
 <button 
   onClick={() => setExpanded(!expanded)}
   className="w-14 h-14 rounded-full flex items-center justify-center"
   style={{ backgroundColor: '#7fe029' }}
 >
   {expanded ? (
     <svg xmlns="http://www.w3.org/2000/svg" 
          className="w-12 h-12 text-white" 
          viewBox="0 0 20 20" 
          fill="currentColor">
       <path fillRule="evenodd" 
             d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" 
             clipRule="evenodd" />
     </svg>
   ) : (
     <svg xmlns="http://www.w3.org/2000/svg" 
          className="w-12 h-12 text-white" 
          viewBox="0 0 20 20" 
          fill="currentColor">
       <path fillRule="evenodd" 
             d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
             clipRule="evenodd" />
     </svg>
   )}
 </button>
</div>

      
        )}
      </div>
    );
  };
  
  return (
    <div className={`rounded-xl overflow-hidden ${darkMode ? 'bg-gray-800 text-gray-100' : 'bg-white text-gray-900'}`}>
      <div className={`px-6 py-5 border-b ${darkMode ? 'border-gray-700 bg-gray-900' : 'border-[#e6f7d9] bg-gradient-to-r from-[#f0fae6] to-white'}`}>
        <div className="flex items-center justify-between">
          <div>
            <h2 className={`text-xl font-roboto ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>Recurring Transactions</h2>
            {/* <p className="mt-1 text-sm text-gray-500">Manage your recurring payments and subscriptions</p> */}
          </div>
          {/* <div className="h-12 w-12 rounded-full bg-[#f0fae6] flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#7fe029]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </div> */}
        </div>
      </div>
      
      <div className="px-6 pt-5 pb-4">
        {/* Modern Special Tab Design - Start */}
        <div className="flex justify-center mb-6">
          <div className={`relative rounded-full p-1 flex ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
            {/* This Month Tab */}
            <button
              onClick={() => setActiveTab('thisMonth')}
              className="relative flex items-center px-4 py-2 text-sm font-medium transition-all duration-300 ease-in-out"
            >
              <span className={`absolute inset-0 ${activeTab === 'thisMonth' ? 'opacity-100' : 'opacity-0'} rounded-full shadow-md bg-gradient-to-r from-[#7fe029] to-[#5cb118] transition-opacity duration-300 ease-in-out`} />

              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-5 w-5 mr-2 z-10 transition-colors duration-300 ${activeTab === 'thisMonth' ? 'text-white' : darkMode ? 'text-gray-300' : 'text-gray-500'}`}
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
              </svg>

              <span className={`relative z-10 transition-colors duration-300 ${activeTab === 'thisMonth' ? 'text-white' : darkMode ? 'text-gray-100' : 'text-gray-700'}`}>
                This Month
              </span>

              <span className={`ml-2 relative z-10 px-2 py-0.5 text-xs rounded-full font-medium transition-colors duration-300 ${activeTab === 'thisMonth'
                  ? 'bg-white text-[#5cb118]'
                  : darkMode
                    ? 'bg-gray-800 text-gray-300'
                    : 'bg-white text-gray-600'
                }`}>
                {thisMonthTransactions.length}
              </span>
            </button>

            {/* Future Tab */}
            <button
              onClick={() => setActiveTab('future')}
              className="relative flex items-center px-4 py-2 text-sm font-medium transition-all duration-300 ease-in-out"
            >
              <span className={`absolute inset-0 ${activeTab === 'future' ? 'opacity-100' : 'opacity-0'} rounded-full shadow-md bg-gradient-to-r from-[#7fe029] to-[#5cb118] transition-opacity duration-300 ease-in-out`} />

              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-5 w-5 mr-2 z-10 transition-colors duration-300 ${activeTab === 'future' ? 'text-white' : darkMode ? 'text-gray-300' : 'text-gray-500'}`}
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>

              <span className={`relative z-10 transition-colors duration-300 ${activeTab === 'future' ? 'text-white' : darkMode ? 'text-gray-100' : 'text-gray-700'}`}>
                Future
              </span>

              <span className={`ml-2 relative z-10 px-2 py-0.5 text-xs rounded-full font-medium transition-colors duration-300 ${activeTab === 'future'
                  ? 'bg-white text-[#5cb118]'
                  : darkMode
                    ? 'bg-gray-800 text-gray-300'
                    : 'bg-white text-gray-600'
                }`}>
                {futureTransactions.length}
              </span>
            </button>
          </div>
        </div>
        {/* Modern Special Tab Design - End */}

        {activeTab === 'thisMonth'
          ? renderTransactions(thisMonthTransactions)
          : renderTransactions(futureTransactions)
        }
      </div>


      <div className={`px-6 py-3 flex justify-between items-center border-t 
  ${darkMode ? 'bg-gray-900 border-gray-700' : 'bg-[#f8fcf2] border-[#e6f7d9]'}`}>

        <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>
          Showing {activeTab === 'thisMonth'
            ? (expanded ? thisMonthTransactions.length : Math.min(5, thisMonthTransactions.length))
            : (expanded ? futureTransactions.length : Math.min(5, futureTransactions.length))
          } of {activeTab === 'thisMonth' ? thisMonthTransactions.length : futureTransactions.length} transactions
        </span>

        <a
          href="#"
          className={`text-sm font-medium flex items-center 
      ${darkMode ? 'text-[#a0e268] hover:text-[#c8f48a]' : 'text-[#5cb118] hover:text-[#7fe029]'}`}
        >
          <span>Manage Transactions</span>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
          </svg>
        </a>
      </div>
    </div>
  );
};

export default RecurringTransactionsCard;