import React, { useEffect, useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { axiosInstance } from  'logic/api/axiosConfig';
import { getCurrentUserId } from '../../utils/AuthUtil';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { FiChevronRight, FiChevronDown } from 'react-icons/fi';
import PaymentLoader from '../load/PaymentLoader';
import { themeClasses } from '../../utils/tailwindUtils'; // Adjust path as needed

const stripePromise = loadStripe('pk_test_51RClAOQ8d4Ag6P7HC740kc7XEwbKiuKx5K3O2S8wmppDbxMz22C3ABjGFLhROwHz6UcOF0gpYdip3NLni2GzEho800D0rMU61T');

const CardSection = ({ darkMode }) => (
  <div className="p-4">
    <label className={`block text-sm font-medium mb-2 ${themeClasses.container(darkMode)}`}>Enter Card Info</label>
    <CardElement className={`p-2 border rounded ${themeClasses.cardInput(darkMode)}`} />
  </div>
);

const StripeDashboardInner = ({ darkMode }) => {
  const userId = getCurrentUserId();
  console.log("userId in StripeDashboardInner: ", userId);

  const elements = useElements();
  const stripe = useStripe();

  const [clientSecret, setClientSecret] = useState('');
  const [message, setMessage] = useState('');
  const [subscriptionInfo, setSubscriptionInfo] = useState(null);
  const [products, setProducts] = useState([]);
  const [priceId, setPriceId] = useState('');
  const [frequency, setFrequency] = useState('');
  const [savedCards, setSavedCards] = useState([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');
  // const [showInvoices, setShowInvoices] = useState(false);
  // const [invoices, setInvoices] = useState([]);
  // const [showInvoiceUpcoming, setShowInvoiceUpcoming] = useState(false);
  // const [invoiceUpcoming, setInvoiceUpcoming] = useState(null);
  const [invoicePreview, setInvoicePreview] = useState(null);
  const [isInvoiceOpen, setIsInvoiceOpen] = useState(true);
  const [isLoadingInvoice, setIsLoadingInvoice] = useState(false);
  const [isDisabled, setIsDisabled] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  // States for Start Subscription button
  const [startSubscriptionLoading, setStartSubscriptionLoading] = useState(false);
  const [startSubscriptionSuccess, setStartSubscriptionSuccess] = useState('');
  const [startSubscriptionError, setStartSubscriptionError] = useState('');
  // States for Save (combined Update Subscription and Set Default Payment)
  const [saveLoading, setSaveLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState('');
  const [saveError, setSaveError] = useState('');
  // States for Cancel/Resume Subscription
  const [cancelResumeSubscriptionLoading, setCancelResumeSubscriptionLoading] = useState(false);
  const [cancelResumeSubscriptionSuccess, setCancelResumeSubscriptionSuccess] = useState('');
  const [cancelResumeSubscriptionError, setCancelResumeSubscriptionError] = useState('');

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch setup intent
        try {
          const setupIntentRes = await axiosInstance.post('/pennypal/api/v1/payment/create-setup-intent', {
            userId,
          });

          const setupIntentData = setupIntentRes.data;
          setClientSecret(setupIntentData.clientSecret);
        } catch (error) {
          throw new Error('Failed to fetch setup intent');
        }

        // Fetch subscription
        try {
          const subscriptionRes = await axiosInstance.get(`/pennypal/api/v1/payment/subscription?userId=${userId}`);
          const subscriptionData = subscriptionRes.data;
          setSubscriptionInfo(subscriptionData || null);
        } catch (error) {
          throw new Error('Failed to fetch subscription');
        }

        // Fetch invoices
        // try {
        //   const invoicesRes = await axiosInstance.get(`/pennypal/api/v1/payment/invoices?userId=${userId}`);
        //   const invoicesData = invoicesRes.data;
        //   setInvoices(invoicesData || []);
        // } catch (error) {
        //   throw new Error('Failed to fetch invoices');
        // }

        // Fetch payment methods
        try {
          const paymentMethodsRes = await axiosInstance.get(`/pennypal/api/v1/payment/payment-methods?userId=${userId}`);
          const paymentMethodsData = paymentMethodsRes.data;
          setSavedCards(paymentMethodsData || []);
        } catch (error) {
          throw new Error('Failed to fetch payment methods');
        }

        // Fetch products
        try {
          const productsRes = await axiosInstance.get('/pennypal/api/v1/payment/products');
          const productsData = productsRes.data;
          setProducts(productsData || []);
        } catch (error) {
          throw new Error('Failed to fetch products');
        }
        
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchData();
    }
  }, [userId]);

  // useEffect(() => {
  //   if (subscriptionInfo?.customerId && subscriptionInfo?.subscriptionId) {
  //     const fetchInvoiceUpcoming = async () => {
  //       try {
  //         const invoiceUpcomingRes = await axiosInstance.post(
  //           '/pennypal/api/v1/payment/invoice/upcoming',
  //           {
  //             userId,
  //             customerId: subscriptionInfo.customerId,
  //             subscriptionId: subscriptionInfo.subscriptionId,
  //           }
  //         );
  //         const invoiceUpcomingData = invoiceUpcomingRes.data;
  //         setInvoiceUpcoming(invoiceUpcomingData || null);
  //       } catch (error) {
  //         console.error('Failed to fetch upcoming invoice');
  //         setInvoiceUpcoming(null);
  //       }
  //     };

  //     fetchInvoiceUpcoming();
  //   }
  // }, [subscriptionInfo?.customerId, subscriptionInfo?.subscriptionId]);

  useEffect(() => {
    console.log("subscriptionInfo in useEffect: ", subscriptionInfo);
    if (subscriptionInfo?.cancelAt !== "null") {
      setIsDisabled(true);
    } else {
      setIsDisabled(false);
    }
  }, [subscriptionInfo?.cancelAt]);

  const handleSubmit = async (e) => {
    setStartSubscriptionLoading(true);
    e.preventDefault();
    setMessage('');
    setStartSubscriptionSuccess('');
    setStartSubscriptionError('');

    let paymentMethodId = selectedPaymentMethod;

    if (!paymentMethodId) {
      const cardElement = elements.getElement(CardElement);
      const { setupIntent, error } = await stripe.confirmCardSetup(clientSecret, {
        payment_method: { card: cardElement }
      });

      if (error) {
        setStartSubscriptionError(error.message);
        setStartSubscriptionLoading(false);
        return;
      }

      paymentMethodId = setupIntent.payment_method;
    }

    try {
      const res = await axiosInstance.post('/pennypal/api/v1/payment/start-subscription', {
        userId,
        paymentMethodId,
        priceId,
        frequency
      });
      const msg = await res.data;
      setStartSubscriptionSuccess(msg || 'Subscription started successfully.');
    } catch (err) {
      setStartSubscriptionError('Subscription failed.');
    } finally {
      setStartSubscriptionLoading(false);
    }
  };

  const handleSave = async () => {
    setSaveLoading(true);
    setSaveSuccess('');
    setSaveError('');

    try {
      let paymentMethodId = selectedPaymentMethod;

      // Handle setting default payment method if a new card is selected
      if (!paymentMethodId && selectedPaymentMethod === '') {
        const cardElement = elements.getElement(CardElement);
        const { setupIntent, error } = await stripe.confirmCardSetup(clientSecret, {
          payment_method: { card: cardElement }
        });

        if (error) {
          setSaveError(error.message);
          setSaveLoading(false);
          return;
        }

        paymentMethodId = setupIntent.payment_method;
      }

      // Update subscription if priceId has changed
      if (priceId && priceId !== subscriptionInfo?.priceId) {
        const res = await axiosInstance.post('/pennypal/api/v1/payment/update-subscription', {
          userId,
          subscriptionId: subscriptionInfo.subscriptionId,
          priceId
        });
        const msg = await res.data;
        setSaveSuccess(msg || 'Subscription updated successfully.');
      }

      // Set default payment method if selectedPaymentMethod has changed
      if (paymentMethodId && paymentMethodId !== subscriptionInfo?.defaultPaymentMethod) {
        const res = await axiosInstance.post('/pennypal/api/v1/payment/set-default-payment-method', {
          userId,
          paymentMethodId
        });
        const msg = await res.data;
        setSaveSuccess(prev => prev ? `${prev} Default payment method set successfully.` : 'Default payment method set successfully.');
      }

      if (!priceId && !paymentMethodId) {
        setSaveError('No changes to save.');
      }
    } catch (err) {
      setSaveError(err.message || 'Failed to save changes.');
    } finally {
      setSaveLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    setCancelResumeSubscriptionLoading(true);
    setCancelResumeSubscriptionSuccess('');
    setCancelResumeSubscriptionError('');
    try {
      const res = await axiosInstance.post('/pennypal/api/v1/payment/cancel-subscription', {
        userId,
        subscriptionId: subscriptionInfo.subscriptionId,
        priceId
      });
      const msg = await res.data;
      setCancelResumeSubscriptionSuccess(msg || 'Subscription canceled successfully.');
    } catch (err) {
      setCancelResumeSubscriptionError(err.message || 'Failed to cancel subscription.');
    } finally {
      setCancelResumeSubscriptionLoading(false);
    }
  };

  const handleResumeSubscription = async () => {
    setCancelResumeSubscriptionLoading(true);
    setCancelResumeSubscriptionSuccess('');
    setCancelResumeSubscriptionError('');
    try {
      const res = await axiosInstance.post('/pennypal/api/v1/payment/resume-subscription', {
        userId,
        subscriptionId: subscriptionInfo.subscriptionId,
        priceId
      });
      const msg = await res.data;
      setCancelResumeSubscriptionSuccess(msg || 'Subscription resumed successfully.');
    } catch (err) {
      setCancelResumeSubscriptionError(err.message || 'Failed to resume subscription.');
    } finally {
      setCancelResumeSubscriptionLoading(false);
    }
  };

  // const handleSetDefaultPayment = async () => {
  //   setSetDefaultPaymentLoading(true);
  //   setSetDefaultPaymentSuccess('');
  //   setSetDefaultPaymentError('');
  //   let paymentMethodId = selectedPaymentMethod;

  //   if (!paymentMethodId) {
  //     const cardElement = elements.getElement(CardElement);
  //     const { setupIntent, error } = await stripe.confirmCardSetup(clientSecret, {
  //       payment_method: { card: cardElement }
  //     });

  //     if (error) {
  //       setSetDefaultPaymentError(error.message);
  //       setSetDefaultPaymentLoading(false);
  //       return;
  //     }

  //     paymentMethodId = setupIntent.payment_method;
  //   }

  //   try {
  //     const res = await axiosInstance.post('/pennypal/api/v1/payment/set-default-payment-method', {
  //       userId,
  //       paymentMethodId
  //     });
  //     const msg = await res.text();
  //     setSetDefaultPaymentSuccess(msg || 'Default payment method set successfully.');
  //   } catch (err) {
  //     setSetDefaultPaymentError(err.message || 'Failed to set default payment method.');
  //   } finally {
  //     setSetDefaultPaymentLoading(false);
  //   }
  // };

  useEffect(() => {
    const fetchInvoicePreview = async () => {
      if (!priceId || !subscriptionInfo?.subscriptionId || !subscriptionInfo?.customerId) {
        setInvoicePreview(null);
        return;
      }

      setIsLoadingInvoice(true);
      try {
        const res = await axiosInstance.post('/pennypal/api/v1/payment/generate-invoice-preview', {
          userId,
          customerId: subscriptionInfo.customerId,
          subscriptionId: subscriptionInfo.subscriptionId,
          priceId
        });

        const previewData = await res.data;
        setInvoicePreview(previewData);
      } catch (err) {
        console.error('Failed to fetch invoice preview');
        setInvoicePreview(null);
      } finally {
        setIsLoadingInvoice(false);
      }
    };

    fetchInvoicePreview();
  }, [priceId]);

  const matchedProduct = products.find(
    product => product.priceId === subscriptionInfo.priceId
  );

  useEffect(() => {
    const defaultCard = savedCards.find((card) => card.isDefault === 'true');
    if (defaultCard) {
      setSelectedPaymentMethod(defaultCard.id);
    }
  }, [savedCards]);

  const handlePlanSelect = (plan) => {
    setPriceId(plan.priceId);
    setFrequency(plan.frequencyCharge || 'monthly');
  };

  if (loading) {
    return (
      <div className={`min-h-screen w-full p-5 flex flex-col justify-center items-center ${themeClasses.container(darkMode)}`}>
        <PaymentLoader darkMode={darkMode} />
        {/* <p className={`mt-2 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Loading subscription details...
        </p> */}
      </div>
    );
  }

  if (error) {
    return (
      <div className={`w-full max-w-4xl mx-auto p-4 ${themeClasses.container(darkMode)}`}>
        <div className={`border px-4 py-3 rounded relative ${themeClasses.error(darkMode)}`} role="alert">
          <strong className={`font-bold ${darkMode ? 'text-red-400' : 'text-red-700'}`}>Error:</strong>
          <span className={`block sm:inline ${darkMode ? 'text-red-300' : 'text-red-700'}`}> {error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-2 p-2 ${themeClasses.container(darkMode)}`}>
      {['active', 'trialing'].includes(subscriptionInfo?.status) ? (
        <>
          <h2 className="text-2xl mb-4">Subscription</h2>
          <h2 className="text-xl font-bold mt-6 mb-4">Subscription Status</h2>
          <div className={`p-4 border rounded-lg ${themeClasses.cardContainer(darkMode)} ${themeClasses.border(darkMode)}`}>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium">Plan:</span>
                <span>{matchedProduct?.productName || 'Unknown'}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Status:</span>
                <span>
                  {subscriptionInfo?.status === 'active' && !isDisabled && (
                    <span className={`text-sm font-semibold py-1 px-2 rounded ${themeClasses.subscriptionBadgeActive(darkMode)}`}>
                      Active
                    </span>
                  )}
                  {subscriptionInfo?.status === 'trialing' && (
                    <span className={`text-sm font-semibold px-2 py-1 rounded ${themeClasses.subscriptionBadgeTrialing(darkMode)}`}>
                      Trialing
                    </span>
                  )}
                  {isDisabled && (
                    <span className={`text-sm font-semibold px-2 py-1 rounded ${themeClasses.subscriptionBadgeCanceled(darkMode)}`}>
                      To Be Canceled
                    </span>
                  )}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Amount:</span>
                <span>$ {(+subscriptionInfo.amount / 100).toFixed(2)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">
                  {isDisabled ? 'Cancellation Date:' : 'Next Billing Date:'}
                </span>
                <span>
                  {isDisabled
                    ? subscriptionInfo.cancelAt.substring(0, 10)
                    : subscriptionInfo.currentPeriodEnd.substring(0, 10)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Stripe Balance:</span>
                <span>$ {(+subscriptionInfo.stripeBalance / 100).toFixed(2)}</span>
              </div>
            </div>
          </div>
          {/* <div
            className={`p-4 border rounded-lg ${
              darkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-300 bg-gray-50'
            }`}
          >
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium">Plan:</span>
                <span>{matchedProduct?.productName || 'Unknown'}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Status:</span>
                <span>
                  {subscriptionInfo?.status === 'active' && !isDisabled && (
                    <span className="text-green-700 bg-green-100 text-sm font-semibold py-1 px-2 rounded">
                      Active
                    </span>
                  )}
                  {subscriptionInfo?.status === 'trialing' && (
                    <span className="text-purple-700 bg-purple-100 text-sm font-semibold px-2 py-1 rounded">
                      Trialing
                    </span>
                  )}
                  {isDisabled && (
                    <span className="text-red-700 bg-red-100 text-sm font-semibold px-2 py-1 rounded">
                      To Be Canceled
                    </span>
                  )}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Amount:</span>
                <span>$ {(+subscriptionInfo.amount / 100).toFixed(2)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">
                  {isDisabled ? 'Cancellation Date:' : 'Next Billing Date:'}
                </span>
                <span>
                  {isDisabled
                    ? subscriptionInfo.cancelAt.substring(0, 10)
                    : subscriptionInfo.currentPeriodEnd.substring(0, 10)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Stripe Balance:</span>
                <span>$ {(+subscriptionInfo.stripeBalance / 100).toFixed(2)}</span>
              </div>
            </div>
          </div> */}

          <label className="block text-xl font-bold mt-4 mb-2">Change Plan</label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {products
              .filter((product) => product.priceId !== subscriptionInfo?.priceId)
              .slice(0, 3) // Limit to 3 plans
              .map((plan) => (
                <div
                  key={plan.priceId}
                  className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                    priceId === plan.priceId ? themeClasses.planCardSelected(darkMode) : themeClasses.planCard(darkMode)
                  }`}
                  onClick={() => handlePlanSelect(plan)}
                >
                  <h3 className="text-lg font-semibold">{plan.productName}</h3>
                  <p className="text-2xl font-bold mt-2">
                    ${plan.unitPrice}
                    <span className="text-sm font-normal">
                      /{plan.frequencyCharge === 'monthly' ? 'month' : 'year'}
                    </span>
                  </p>
                  <ul className="mt-4 space-y-2">
                    <li className="flex items-center">
                      <span className="text-green-500 mr-2">✔</span> Core features
                    </li>
                    <li className="flex items-center">
                      <span className="text-green-500 mr-2">✔</span>{' '}
                      {plan.frequencyCharge === 'monthly' ? 'Monthly' : 'Yearly'} billing
                    </li>
                    <li className="flex items-center">
                      <span className="text-green-500 mr-2">✔</span> 24/7 support
                    </li>
                  </ul>
                  <button
                    className={`mt-4 w-full py-2 rounded ${
                      priceId === plan.priceId ? themeClasses.planButtonSelected(darkMode) : themeClasses.planButton(darkMode)
                    }`}
                  >
                    {priceId === plan.priceId ? 'Selected' : 'Select Plan'}
                  </button>
                </div>
              ))}
          </div>
             {/* <div className="flex items-left mr-2 mt-4">
            <button
              onClick={handleUpdateSubscription}
              className={`py-2 px-4 rounded text-white font-semibold bg-[#8bc34a] hover:bg-[#c5e1a5] flex items-center`}
              disabled={isDisabled || updateSubscriptionLoading}
            >
              {updateSubscriptionLoading ? (
                <>
                  <span className="spinner inline-block border-2 border-gray-200 border-t-white rounded-full w-4 h-4 animate-spin mr-2"></span>
                  Saving...
                </>
              ) : (
                'Save'
              )}
            </button>
            {isDisabled && (
              <p className="ml-4 mt-2 py-2 text-sm text-red-500">
                You can't change the plan as you have requested for cancellation.
              </p>
            )}
          </div>
          {updateSubscriptionSuccess && (
            <p className="mt-2 text-sm text-green-600">{updateSubscriptionSuccess}</p>
          )}
          {updateSubscriptionError && (
            <p className="mt-2 text-sm text-red-600">{updateSubscriptionError}</p>
          )} */}

{/* 
          <div className="mt-6">
            <div
              className="flex items-center cursor-pointer select-none text-md font-medium text-gray-800"
            >
              <span onClick={() => setShowInvoices(!showInvoices)}>
                {showInvoices ? <FiChevronDown /> : <FiChevronRight />}
              </span>
              <span className="mr-2">Invoices Paid</span>
              <span className="ml-4" onClick={() => setShowInvoiceUpcoming(!showInvoiceUpcoming)}>
                {showInvoiceUpcoming ? <FiChevronDown /> : <FiChevronRight />}
              </span>
              <span>Upcoming Invoice</span>
            </div>

            {showInvoices && (
              <div className="mt-2 overflow-x-auto">
                <table className="min-w-full text-sm border rounded mt-2">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="px-4 py-2 border rounded">Date</th>
                      <th className="px-4 py-2 border rounded">Amount Paid</th>
                      <th className="px-4 py-2 border rounded">Reason</th>
                      <th className="px-4 py-2 border rounded">Invoice</th>
                      <th className="px-4 py-2 border rounded">PDF</th>
                    </tr>
                  </thead>
                  <tbody>
                    {invoices.map((invoice) => (
                      <tr key={invoice.invoiceId} className="text-center">
                        <td className="px-4 py-2 border rounded">{invoice.createdAt.substring(0, 10)}</td>
                        <td className="px-4 py-2 border rounded">$ {Number(invoice.amountPaid).toFixed(2)}</td>
                        <td className="px-4 py-2 border rounded">{invoice.billingReason.replace('_', ' ')}</td>
                        <td className="px-4 py-2 border rounded">
                          <a
                            href={invoice.invoiceUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 underline"
                          >
                            Invoice Link
                          </a>
                        </td>
                        <td className="px-4 py-2 border rounded">
                          <a
                            href={invoice.invoicePdf}
                            target="_blank"
                            rel="noopener noreferrer"
                            title="Download PDF"
                            className="text-gray-700 hover:text-black"
                          >
                            🧾
                          </a>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {showInvoiceUpcoming && (
              <div className="mt-2 transition-all duration-300 ease-in-out">
                  {invoiceUpcoming == null ? (
                    <p>No upcoming invoice</p>
                  ) : (
                  <table className="w-full text-sm mb-2">
                    <thead>
                      <tr className="text-left border-b">
                        <th className="py-1">Description</th>
                        <th className="py-1 text-right">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      {invoiceUpcoming.items.map((item, idx) => (
                        <tr key={idx} className="border-b">
                          <td className="py-1">{item.description}</td>
                          <td className={`py-1 text-right $ {item.credit ? 'text-red-600' : 'text-green-600'}`}>
                            {item.credit ? '-' : '+'}$ {Math.abs(parseFloat(item.amount)).toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="font-bold">
                        <td className="py-1">Total Due on {invoiceUpcoming.nextPaymentDate.substring(0, 10)}</td>
                        <td className="py-1 text-right">$ {parseFloat(invoiceUpcoming.totalAmount).toFixed(2)} {invoiceUpcoming.currency}</td>
                      </tr>
                    </tfoot>
                  </table>
                  )}
              </div>
            )}
          </div> */}

          {/* <label className="block text-md font-medium font-semibold mt-4 mb-2">Change Plan</label>
          <select
            value={priceId}
            onChange={(e) => {
              setPriceId(e.target.value)
              setFrequency(e.target.value === matchedProduct?.priceId ? matchedProduct?.frequencyCharge : "monthly")
            }}
            className="p-2 border rounded"
          >
            <option value="">Change Plan</option>
            {products
              .filter((product) => product.priceId !== subscriptionInfo.priceId).map((product) => (
                <option key={product.priceId} value={product.priceId}>
                  {product.productName} ($ {product.unitPrice}/{product.frequencyCharge === "monthly" ? "month" : "year"})
                </option>
            ))}
          </select> */}

          {invoicePreview && (
            <div className={`mt-4 ${themeClasses.container(darkMode)}`}>
              <button
                className={`flex items-center cursor-pointer select-none text-md font-medium ${themeClasses.container(darkMode)}`}
                onClick={() => setIsInvoiceOpen((prev) => !prev)}
              >
                <span className="mr-2">
                  {isInvoiceOpen ? <FiChevronDown /> : <FiChevronRight />}
                </span>
                <span>Invoice Preview</span>
              </button>

              {isInvoiceOpen && (
                <div className="transition-all duration-300 ease-in-out">
                  {isLoadingInvoice ? (
                    <div className="flex justify-center py-4">
                      <div className={`spinner border-t-2 border-b-2 w-5 h-5 rounded-full animate-spin ${themeClasses.spinner(darkMode)}`}></div>
                    </div>
                  ) : (
                    <table className={`w-full text-sm mb-2 ${themeClasses.border(darkMode)}`}>
                      <thead>
                        <tr className={`text-left border-b  ${themeClasses.border(darkMode)}`}>
                          <th className="py-1">Description</th>
                          <th className="py-1 text-right">Amount</th>
                        </tr>
                      </thead>
                      <tbody>
                        {invoicePreview.items.map((item, idx) => (
                          <tr key={idx} className={`border-b ${themeClasses.border(darkMode)} ${themeClasses.container(darkMode)}`}>
                            <td className="py-1">{item.description}</td>
                            <td className={`py-1 text-right ${item.credit ? themeClasses.amountCredit(darkMode) : themeClasses.amountDebit(darkMode)}`}>
                              {item.credit ? '-' : '+'}$ {Math.abs(parseFloat(item.amount)).toFixed(2)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr className={`font-bold ${themeClasses.container(darkMode)}`}>
                          <td className="py-1">Total</td>
                          <td className="py-1 text-right">$ {parseFloat(invoicePreview.totalAmount).toFixed(2)} {invoicePreview.currency}</td>
                        </tr>
                      </tfoot>
                    </table>
                  )}
                </div>
              )}
            </div>
          )}
{/* 
          <div className="flex items-left mr-2 mt-4">
            <button
              onClick={handleUpdateSubscription}
              className="bg-green-100 px-2 py-1 border border-green-600 rounded mt-2 font-semibold flex items-center"
              disabled={isDisabled || updateSubscriptionLoading}
            >
              {updateSubscriptionLoading ? (
                <>
                  <span className="spinner inline-block border-2 border-gray-200 border-t-green-600 rounded-full w-4 h-4 animate-spin mr-2"></span>
                  Saving...
                </>
              ) : (
                'Save'
              )}
            </button>
            {isDisabled && (
              <p className="ml-4 mt-2 py-2 text-sm text-red-500">
                You can't change the plan as you have requested for cancellation.
              </p>
            )}
          </div>
          {updateSubscriptionSuccess && (
            <p className="mt-2 text-sm text-green-600">{updateSubscriptionSuccess}</p>
          )}
          {updateSubscriptionError && (
            <p className="mt-2 text-sm text-red-600">{updateSubscriptionError}</p>
          )} */}

          <label className="block text-xl font-bold mt-4 mb-2">
            Change Default Payment Method
          </label>
                    <div className={`space-y-2 border rounded w-1/2 inline-block p-2 ${themeClasses.border(darkMode)} ${themeClasses.cardContainer(darkMode)}`}>
            {savedCards.length === 0 ? (
              <p className="text-gray-500">No saved cards</p>
            ) : (
              savedCards.map((card) => (
                <label
                  key={card.id}
                  className={`flex items-center p-2 cursor-pointer ${themeClasses.hover(darkMode)}`}
                >
                  <input
                    type="radio"
                    name="savedCard"
                    value={card.id}
                    checked={selectedPaymentMethod === card.id}
                    onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                    className="mr-3"
                  />
                  <div className="flex flex-col">
                    <div className="flex items-center">
                      <span className="font-medium">
                        {card.brand.toUpperCase()} **** {card.last4}
                      </span>
                      {card.isDefault === 'true' && (
                        <span className={`ml-2 text-xs font-semibold px-2 py-1 rounded ${themeClasses.subscriptionBadgeActive(darkMode)}`}>
                          Default
                        </span>
                      )}
                    </div>
                    <span className="text-sm text-gray-600">
                      Expires {card.expMonth}/{card.expYear}
                    </span>
                  </div>
                </label>
              ))
            )}
            <label className={`flex items-center p-3 cursor-pointer ${themeClasses.hover(darkMode)}`}>
              <input
                type="radio"
                name="savedCard"
                value=""
                checked={selectedPaymentMethod === ''}
                onChange={() => setSelectedPaymentMethod('')}
                className="mr-3"
              />
              <div className="flex flex-col">
                <span className="font-medium">Use a New Card</span>
              </div>
            </label>
            {!selectedPaymentMethod && <CardSection darkMode={darkMode} />}
          </div>

          <div className="flex justify-center gap-4 mt-6">
            <button
              onClick={handleSave}
              className={`py-2 px-4 rounded font-semibold ${themeClasses.planButtonSelected(darkMode)} flex items-center`}
              disabled={isDisabled || saveLoading}
            >
              {saveLoading ? (
                <>
                  <span className={`spinner inline-block border-2 rounded-full w-4 h-4 animate-spin mr-2 ${themeClasses.spinner(darkMode)}`}></span>
                  Saving...
                </>
              ) : (
                'Save'
              )}
            </button>
            {/* {isDisabled && (
              <p className="ml-4 mt-2 py-2 text-sm text-red-500">
                You can't change the payment method as you have requested for cancellation.
              </p>
            )}
          </div>
          {setDefaultPaymentSuccess && (
            <p className="mt-2 text-sm text-green-600">{setDefaultPaymentSuccess}</p>
          )}
          {setDefaultPaymentError && (
            <p className="mt-2 text-sm text-red-600">{setDefaultPaymentError}</p>
          )}

          <div className="mt-6"> */}
            {!isDisabled ? (
              <button
                onClick={handleCancelSubscription}
                className={`py-2 px-4 rounded font-semibold ${themeClasses.planButtonSelected(darkMode)} flex items-center`}
                disabled={cancelResumeSubscriptionLoading}
              >
                {cancelResumeSubscriptionLoading ? (
                  <>
                    <span className={`spinner inline-block border-2 rounded-full w-4 h-4 animate-spin mr-2 ${themeClasses.spinner(darkMode)}`}></span>
                    Canceling...
                  </>
                ) : (
                  'Cancel Subscription'
                )}
              </button>
            ) : (
              <button
                onClick={handleResumeSubscription}
                className={`py-2 px-4 rounded font-semibold ${themeClasses.planButtonSelected(darkMode)} flex items-center`}
                disabled={cancelResumeSubscriptionLoading}
              >
                {cancelResumeSubscriptionLoading ? (
                  <>
                    <span className={`spinner inline-block border-2 rounded-full w-4 h-4 animate-spin mr-2 ${themeClasses.spinner(darkMode)}`}></span>
                    Resuming...
                  </>
                ) : (
                  'Resume Subscription'
                )}
              </button>
            )}
          </div>

          {isDisabled && (
            <p className={`mt-2 text-sm text-center ${themeClasses.errorText(darkMode)}`}>
              You can't make changes as you have requested for cancellation.
            </p>
          )}
          {saveSuccess && (
            <p className={`mt-2 text-sm text-center ${themeClasses.successText(darkMode)}`}>{saveSuccess}</p>
          )}
          {saveError && (
            <p className={`mt-2 text-sm text-center ${themeClasses.errorText(darkMode)}`}>{saveError}</p>
          )}
          {cancelResumeSubscriptionSuccess && (
            <p className={`mt-2 text-sm text-center ${themeClasses.successText(darkMode)}`}>{cancelResumeSubscriptionSuccess}</p>
          )}
          {cancelResumeSubscriptionError && (
            <p className={`mt-2 text-sm text-center ${themeClasses.errorText(darkMode)}`}>{cancelResumeSubscriptionError}</p>
          )}
        </>
      ) : (
        <>
          <h2 className="text-xl font-bold mb-4">No Active Subscriptions</h2>
          <form
            onSubmit={handleSubmit}
            className={`max-w-full mx-auto space-y-4 p-6 shadow rounded-lg ${themeClasses.container(darkMode)}`}
          >
              <h2 className="text-xl font-bold mb-4">Start Subscription</h2>
            <label className="block text-md font-semibold mb-4">Select Plan</label>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {products.slice(0, 3).map((plan) => (
                <div
                  key={plan.priceId}
                  className={`p-4 flex flex-col border rounded-lg cursor-pointer items-center text-center transition-all duration-200 ${
                    priceId === plan.priceId ? themeClasses.planCardSelected(darkMode) : themeClasses.planCard(darkMode)
                  }`}
                  onClick={() => handlePlanSelect(plan)}
                >
                  <h3 className="text-lg font-semibold">{plan.productName}</h3>
                  <p className="text-2xl font-bold mt-2">
                    ${plan.unitPrice}
                    <span className="text-sm font-normal">
                      /{plan.frequencyCharge === 'monthly' ? 'month' : 'year'}
                    </span>
                  </p>
                  <ul className="mt-4 space-y-2">
                    <li className="flex items-center">
                      <span className="text-green-500 mr-2">✔</span> Core features
                    </li>
                    <li className="flex items-center">
                      <span className="text-green-500 mr-2">✔</span>{' '}
                      {plan.frequencyCharge === 'monthly' ? 'Monthly' : 'Yearly'} billing
                    </li>
                    <li className="flex items-center">
                      <span className="text-green-500 mr-2">✔</span> 24/7 support
                    </li>
                  </ul>
                  <button
                    className={`mt-4 w-full py-2 rounded ${
                      priceId === plan.priceId ? themeClasses.planButtonSelected(darkMode) : themeClasses.planButton(darkMode)
                    }`}
                  >
                    {priceId === plan.priceId ? 'Selected' : 'Select Plan'}
                  </button>
                </div>
              ))}
            </div>

            <div className={`space-y-2 border rounded w-1/2 inline-block p-4 mt-4 ${themeClasses.border(darkMode)} ${themeClasses.cardContainer(darkMode)}`}>
              {savedCards.length === 0 ? (
                <p className="text-gray-500">No saved cards</p>
              ) : (
                savedCards.map((card) => (
                  <label
                    key={card.id}
                    className={`flex items-center p-3 cursor-pointer ${themeClasses.hover(darkMode)}`}
                  >
                    <input
                      type="radio"
                      name="savedCard"
                      value={card.id}
                      checked={selectedPaymentMethod === card.id}
                      onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                      className="mr-3"
                    />
                    <div className="flex flex-col">
                      <div className="flex items-center">
                        <span className="font-medium">
                          {card.brand.toUpperCase()} **** {card.last4}
                        </span>
                        {card.isDefault === 'true' && (
                          <span className={`ml-2 text-xs font-semibold px-1 py-0.5 rounded ${themeClasses.subscriptionBadgeActive(darkMode)}`}>
                            Default
                          </span>
                        )}
                      </div>
                      <span className="text-sm text-gray-600">
                        Expires {card.expMonth}/{card.expYear}
                      </span>
                    </div>
                  </label>
                ))
              )}
              <label className={`flex items-center p-3 cursor-pointer ${themeClasses.hover(darkMode)}`}>
                <input
                  type="radio"
                  name="savedCard"
                  value=""
                  checked={selectedPaymentMethod === ''}
                  onChange={() => setSelectedPaymentMethod('')}
                  className="mr-3"
                />
                <div className="flex flex-col">
                  <span className="font-medium">Use a New Card</span>
                </div>
              </label>
              {!selectedPaymentMethod && <CardSection darkMode={darkMode} />}
            </div>

            <div className="mt-4">
              {startSubscriptionLoading ? (
                <>
                  <span className={`spinner inline-block border-2 rounded-full w-4 h-4 animate-spin mr-2 ${themeClasses.spinner(darkMode)}`}></span>
                  Starting...
                </>
              ) : (
                <button
                  type="submit"
                  className={`py-2 px-4 rounded font-semibold ${themeClasses.planButtonSelected(darkMode)}`}
                >
                  Start Subscription
                </button>
              )}
            </div>

            {startSubscriptionSuccess && (
              <p className={`mt-2 text-sm ${themeClasses.successText(darkMode)}`}>{startSubscriptionSuccess}</p>
            )}
            {startSubscriptionError && (
              <p className={`mt-2 text-sm ${themeClasses.errorText(darkMode)}`}>{startSubscriptionError}</p>
            )}
          </form>
        </>
      )}

      {message && <p className={`mt-4 text-sm ${themeClasses.successText(darkMode)}`}>{message}</p>}
    </div>
  );
};

const StripeDashboard = ({ darkMode }) => (
  <Elements stripe={stripePromise}>
    <StripeDashboardInner darkMode={darkMode} />
  </Elements>
);

export default StripeDashboard;