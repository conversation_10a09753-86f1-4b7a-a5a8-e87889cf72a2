import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { UilSave } from '@iconscout/react-unicons';
import {
  fetchNotificationRules,
  updateEditedRule,
  saveNotificationRule,
} from '../../../../../../logic/redux/notificationSlice';
import { themeClasses } from '../../../../utils/tailwindUtils'; // Adjust path as needed

const NotificationSettingsModal = ({ isOpen, onClose, darkMode }) => {
  const dispatch = useDispatch();
  const { rules, editedRules, loading, error } = useSelector((state) => state.notifications || {});
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (isOpen) {
      dispatch(fetchNotificationRules());
    }
  }, [isOpen, dispatch]);

  if (!isOpen) return null;

  const handleRuleChange = (id, field, value) => {
    dispatch(updateEditedRule({ id, field, value }));
  };

  const handleSave = (rule) => {
    setIsSaving(prev => ({ ...prev, [rule.ruleId]: true }));
    dispatch(saveNotificationRule(editedRules[rule.ruleId]));
    setTimeout(() => setIsSaving(prev => ({ ...prev, [rule.ruleId]: false })), 1000);
  };

  return (
    <div className={`min-h-screen w-full ${themeClasses.pageContainer(darkMode)}`}>
      <div
        className={`rounded p-2 max-h-[90vh] overflow-y-auto scrollbar-none ${themeClasses.pageContainer(darkMode)}`}
      >
        <div className="flex mb-6">
          <h3 className="text-2xl">Notification Rules</h3>
          {/* <button onClick={onClose} className="text-gray-500 hover:text-gray-700">✕</button> */}
        </div>
        <div>
          <table
            className={`min-w-full rounded-t-xl rounded-b-xl overflow-hidden max-w-lg w-full border ${themeClasses.tableContainer(darkMode)}`}
          >
            <thead>
              <tr className={`h-10 ${themeClasses.tableHeaderRow(darkMode)}`}>
                <th className={`py-1 px-2 ${themeClasses.tableCellBorder(darkMode)} text-left w-1/11`}>Enabled</th>
                <th className={`py-1 px-2 ${themeClasses.tableCellBorder(darkMode)} text-left`}>Rule Name</th>
                <th className={`py-1 px-2 ${themeClasses.tableCellBorder(darkMode)} text-left w-1/6`}>Threshold</th>
                <th className={`py-1 px-2 ${themeClasses.tableCellBorder(darkMode)} text-left w-1/8`}>Severity</th>
                <th className={`py-1 px-2 ${themeClasses.tableCellBorder(darkMode)} text-left w-1/14`}>Mobile</th>
                <th className={`py-1 px-2 ${themeClasses.tableCellBorder(darkMode)} text-left w-1/14`}>Email</th>
              </tr>
            </thead>
            <tbody>
              {rules && rules.length > 0 ? (
                rules.map((rule) => {
                  const editedRule = editedRules[rule.ruleId] || rule;
                  const thresholdField =
                    editedRule.conditionType === 'AMOUNT_THRESHOLD'
                      ? 'amountThreshold'
                      : editedRule.conditionType === 'DAYS_BEFORE'
                      ? 'daysThreshold'
                      : 'percentageThreshold';
                  const thresholdValue =
                    editedRule[thresholdField] !== undefined ? editedRule[thresholdField] : rule[thresholdField] || '';
                  const thresholdSuffix =
                    editedRule.conditionType === 'AMOUNT_THRESHOLD'
                      ? '$'
                      : editedRule.conditionType === 'DAYS_BEFORE'
                      ? 'Days'
                      : editedRule.conditionType === 'PERCENTAGE_THRESHOLD'
                      ? '%'
                      : '';
                  // const editedRule = editedRules[rule.ruleId] || rule;
                  // const thresholdField = editedRule.conditionType === 'AMOUNT_THRESHOLD' ? 'amountThreshold' :
                  //   editedRule.conditionType === 'DAYS_BEFORE' ? 'daysThreshold' :
                  //     'percentageThreshold';
                  // const thresholdValue = editedRule[thresholdField] !== undefined ? editedRule[thresholdField] : (rule[thresholdField] || '');
                  // const thresholdSuffix = editedRule.conditionType === 'AMOUNT_THRESHOLD' ? '$: ' :
                  //   editedRule.conditionType === 'DAYS_BEFORE' ? ' Days: ' :
                  //     editedRule.conditionType === 'PERCENTAGE_THRESHOLD' ? '%: ' : '';
                  return (
                    <tr
                      key={rule.ruleId}
                      className={`${themeClasses.tableRow(darkMode)} ${themeClasses.tableRowHover(darkMode)}`}
                    >
                      <td className={`py-1 px-2 ${themeClasses.tableCellBorder(darkMode)}`}>
                        <input
                          type="checkbox"
                          checked={editedRule.isEnabled}
                          onChange={(e) => handleRuleChange(rule.ruleId, 'isEnabled', e.target.checked)}
                          className={`form-checkbox h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
                        />
                      </td>
                      <td className={`py-1 px-2 ${themeClasses.tableCellBorder(darkMode)}`}>
                        <input
                          type="text"
                          value={editedRule.ruleName || ''}
                          disabled
                          className={`font-sm w-full ${themeClasses.inputField(darkMode)}`}
                        />
                        <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          {editedRule.ruleName === 'Recurring Payment Due Soon' && 'Notifies when a recurring payment is due within the set days.'}
                          {editedRule.ruleName === 'Budget Alert' && 'Notifies when budget usage exceeds the set percentage of allocated.'}
                          {editedRule.ruleName === 'Large Txn Alert' && 'Notifies when transactions exceedes the set amount.'}
                          {editedRule.ruleName === 'New Budget Alert' && 'Notifies when a new budget is created or updated.'}
                          {editedRule.ruleName === 'Account out of sync' && 'Notifies when one of your financial accounts needs to be linked again.'}
                          {editedRule.ruleName === 'New recurring account detected' && 'Notifies when a new account is detected.'}
                          {editedRule.ruleName === 'Recurring account alert' && ''}
                          {editedRule.ruleName === 'Bill due date passed alert' && 'Notifies when a payment is due.'}
                        </div>
                      </td>
                      <td className={`py-1 px-2 ${themeClasses.tableCellBorder(darkMode)}`}>
                        <span className="ml-0 text-gray-600">{thresholdSuffix}</span>
                        <input
                          type="number"
                          value={thresholdValue}
                          onChange={(e) => {
                            const value = parseFloat(e.target.value) || 0;
                            handleRuleChange(rule.ruleId, thresholdField, value);
                          }}
                          className={`p-1 w-1/2 rounded ${themeClasses.inputField(darkMode)}`}
                          min="0"
                        />
                      </td>
                      <td className={`py-1 px-2 ${themeClasses.tableCellBorder(darkMode)}`}>
                        <select
                          value={editedRule.severity}
                          onChange={(e) => handleRuleChange(rule.ruleId, 'severity', e.target.value)}
                          className={`p-1 w-full border rounded-md focus:outline-none ${themeClasses.selectField(
                            darkMode,
                          )} ${themeClasses.inputFocusRing(darkMode)}`}
                        >
                          <option value="LOW">Low</option>
                          <option value="MEDIUM">Medium</option>
                          <option value="HIGH">High</option>
                          <option value="CRITICAL">Critical</option>
                        </select>
                      </td>
                      <td className={`py-1 px-2 ${themeClasses.tableCellBorder(darkMode)}`}>
                        <input
                          type="checkbox"
                          checked={editedRule.phoneEnabled}
                          onChange={(e) => handleRuleChange(rule.ruleId, 'phoneEnabled', e.target.checked)}
                          className={`form-checkbox h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
                        />
                      </td>
                      <td className={`py-1 px-2 ${themeClasses.tableCellBorder(darkMode)}`}>
                        <input
                          type="checkbox"
                          checked={editedRule.emailEnabled}
                          onChange={(e) => handleRuleChange(rule.ruleId, 'emailEnabled', e.target.checked)}
                          className={`form-checkbox h-4 w-4 ${themeClasses.checkboxInput(darkMode)}`}
                        />
                      </td>
                      {/* <td className="py-1 px-2 text-center">
                        <button
                          className={`py-1 px-2 rounded flex items-center justify-center text-white ${isSaving[rule.ruleId]
                            ? darkMode ? 'bg-blue-600' : 'bg-blue-500'
                            : darkMode ? 'bg-blue-500 hover:bg-blue-600' : 'bg-blue-500 hover:bg-blue-600'
                            }`}
                          disabled={isSaving[rule.ruleId]}
                        >

                          {isSaving[rule.ruleId] ? (
                            <div
                              className="w-4 h-4 border-2 border-t-2 border-white rounded-full"
                              style={{ animation: 'spin 1s linear infinite' }}
                            ></div>
                          ) : (
                            <>
                              <UilSave size="14" className="mr-1" />
                            </>
                          )}
                        </button>
                      </td> */}
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td
                    colSpan="6"
                    className={`py-2 text-center ${themeClasses.emptyVisualization(darkMode)}`}
                  >
                    No notification rules found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        <div className="mt-4 flex justify-center">
          <button
            className={`py-2 px-4 rounded font-semibold ${themeClasses.reconcileButton(darkMode)}`}
            onClick={() => {
              setIsSaving(true);
              Object.keys(editedRules).forEach((ruleId) => {
                dispatch(saveNotificationRule(editedRules[ruleId]));
              });
              setTimeout(() => setIsSaving(false), 1000);
            }}
            disabled={isSaving}
          >
            {isSaving ? (
              <div
                className={`w-4 h-4 rounded-full animate-spin ${themeClasses.spinner(darkMode)}`}
              ></div>
            ) : (
              'Save All'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default NotificationSettingsModal;