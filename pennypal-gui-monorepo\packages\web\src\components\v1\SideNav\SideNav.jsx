import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useLocation, useNavigate } from 'react-router-dom';
// import { logEvent } from '../../../utils/EventLogger';
import { getPermissions } from '../../../utils/PermUtil';
import {
  UilBars,
  UilClipboardAlt,
  UilDollarSign,
  UilFile,
  UilWallet,
  UilReceipt,
  UilHome,
  UilUser,
  UilUsersAlt,  // Icon for "Add to Family"
  UilSetting,   // Icon for "Settings"
  // UilMoneyBillAlt,  // Icon for "Cash Flow"
  UilBell,
  UilTrashAlt,
  UilTransaction,
  UilDashboard,
  UilBookOpen,
  UilSave,
  UilMoon,
  UilSun,
  UilCreditCard, // Icon for Stripe
  UilCommentAlt, // Icon for Chatbot
   UilCheckSquare, // Updated for Reconcile
  Uil<PERSON>hart, // Added for Investment
  UilBullseye, // Added for Goals

} from '@iconscout/react-unicons';
import { FaMoneyBillAlt } from 'react-icons/fa';

import logo from '../../../assets/pennypal_logo.png';
import logo_mini from '../../../assets/logo_mini.png';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {  faClipboardList, faDollarSign, faFileAlt, faWallet, faReceipt, faHome, faRepeat,faCog,faBullseye,faSignOutAlt   } from '@fortawesome/free-solid-svg-icons';


import IconMenu from './IconMenu';
import Notifications from './Settings/NotificationRulesModal';
import { fetchNotifications, fetchNotificationRules } from '../../../../../logic/redux/notificationSlice';
import { setToToday } from '../../../../../logic/redux/budgetSlice';
import { getCurrentUserId } from '../../../utils/AuthUtil';

const SideNav = ({ size, callback, slideIn, darkMode, toggleDarkMode, toggleNotifications, togglePennyPalHomepage, toggleLandingpage  }) => {
  console.log("SideNav constructor is called - Size:", size, "Slide In:", slideIn);
  const toggleRef = useRef(null);
  const devtoolsPreviouslyOpen = useRef(false);
  const [isSettingsDropdownOpen, setIsSettingsDropdownOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Fixes for APT-100,101,102,103,108
  // Pre-fetch data for notifications, notification rules, budget, and budget rules on user login
  // and store in redux state
  const userId = getCurrentUserId();
  useEffect(() => {
    dispatch(fetchNotifications({ userId: userId }));
    dispatch(fetchNotificationRules({ userId: userId }));
    dispatch(setToToday());
    dispatch({ type: 'budgetRule/fetchSubcategories', payload: userId });
    dispatch({ type: 'budgetRule/fetchBudgetRules', payload: userId });
    dispatch({ type: 'budgetRule/fetchAccounts', payload: userId });
  }, [dispatch]);
  
  // Local state to track the active page if needed
  const [activePage, setActivePage] = useState('dashboard');
  const permissions = getPermissions();
  const hasPage = (pageName) => {
    if(permissions) {
      return permissions.some(p => p.page === pageName);
    } else {
      return true;
    }
  };

  // Logout function
  const handleLogout = () => {
    try {
      // Clear localStorage/sessionStorage
      localStorage.clear();
      sessionStorage.clear();
      
      // Clear Redux store if you have logout action
      // dispatch(logoutAction());
      
      // Clear any cookies if needed
      document.cookie.split(";").forEach((c) => {
        document.cookie = c
          .replace(/^ +/, "")
          .replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });
      
      // Navigate to login page
      navigate('/login', { replace: true });
      
      // Optional: Force page reload to ensure clean state
      window.location.reload();
      
    } catch (error) {
      console.error('Error during logout:', error);
      // Fallback: force redirect to login
      window.location.href = '/login';
    }
  };
  
  useEffect(() => {
    const threshold = 160;

    const resizeHandler = () => {
      const widthDiff = window.outerWidth - window.innerWidth > threshold;
      const heightDiff = window.outerHeight - window.innerHeight > threshold;
      const devtoolsOpen = widthDiff || heightDiff;

      // Trigger only when transitioning from closed → open
      if (devtoolsOpen && !devtoolsPreviouslyOpen.current) {
        devtoolsPreviouslyOpen.current = true; // Mark as open
        console.log('DevTools just opened — triggering sidebar toggle!');
        if (toggleRef.current) toggleRef.current.click();
      }

      // Reset when DevTools are closed
      if (!devtoolsOpen && devtoolsPreviouslyOpen.current) {
        devtoolsPreviouslyOpen.current = false;
        console.log('DevTools closed.');
      }
    };

    window.addEventListener('resize', resizeHandler);
    return () => window.removeEventListener('resize', resizeHandler);
  }, []);

  // useEffect(() => {
  //   const threshold = 160;

  //   const resizeHandler = () => {
  //     const widthDiff = window.outerWidth - window.innerWidth > threshold;
  //     const heightDiff = window.outerHeight - window.innerHeight > threshold;
  //     const devtoolsOpen = widthDiff || heightDiff;

  //     if (devtoolsOpen && !devtoolsPreviouslyOpen.current) {
  //       devtoolsPreviouslyOpen.current = true;
  //       console.log('DevTools just opened — toggling sidebar');
  //       toggleRef.current?.click();
  //     }

  //     if (!devtoolsOpen && devtoolsPreviouslyOpen.current) {
  //       devtoolsPreviouslyOpen.current = false;
  //       console.log('DevTools just closed — resetting sidebar');
  //       toggleRef.current?.click(); // Toggle back to default
  //     }
  //   };

  //   window.addEventListener('resize', resizeHandler);
  //   return () => window.removeEventListener('resize', resizeHandler);
  // }, []);


  // Determine active link based on the current URL path
  useEffect(() => {
    if (location.pathname.startsWith('/dashboard/accounts')) {
      setActivePage('accounts');
    } else if (location.pathname.startsWith('/dashboard/transactions')) {
      setActivePage('transactions');
    } else if (location.pathname.startsWith('/dashboard/RecurringTransactions')) {
      setActivePage('RecurringTransactions');
    } else if (location.pathname.startsWith('/dashboard/budget')) {
      setActivePage('budget');
    // } else if (location.pathname.startsWith('/dashboard/budget-rules')) {
    //   setActivePage('budget-rules');
    } else if (location.pathname.startsWith('/dashboard/receipts')) {
      setActivePage('receipts');
    } else if (location.pathname.startsWith('/dashboard/profile')) {
      setActivePage('profile');
    } else if (location.pathname.startsWith('/dashboard/family')) {
      setActivePage('family');
    } else if (location.pathname.startsWith('/dashboard/settings')) {
      setActivePage('settings');
    } else if (location.pathname.startsWith('/dashboard/cashflow')) {
      setActivePage('cashflow');
    }
    else if (location.pathname.startsWith('/dashboard/Reconciliation')) {
      setActivePage('Reconciliation');
    } else if (location.pathname.startsWith('/dashboard/bookkeeping')) {
      setActivePage('bookkeeping');
       } else if (location.pathname.startsWith('/dashboard/goals')) {
      setActivePage('goals');
    } else if (location.pathname.startsWith('/dashboard/investment')) {
      setActivePage('investment');
    } else if (location.pathname.startsWith('/dashboard/investmentsdashboard')) {
    setActivePage('investmentsdashboard'); // Add this line
  } else if (location.pathname.startsWith('/dashboard/stripe')) {
      setActivePage('stripe');
    } else if (location.pathname.startsWith('/dashboard/chatbot')) {
      setActivePage('chatbot');
    } else if (location.pathname.startsWith('/dashboard/custom-charts')) {
      setActivePage('custom-charts');
       } else if (location.pathname.startsWith('/dashboard/receiptitems')) {
      setActivePage('receipt-items');
    } else {
      setActivePage('dashboard');
    }
  }, [location.pathname]);



  // return () => {
  //   if (currentObserver) {
  //     currentObserver.disconnect();
  //   }
  // };

  const theme = {
    containerClr: '',
    canvasClr: '',
    sideNavMnuClr: '',
    sideNavHdrClr: '',
  };

  // Define your icons using Iconscout Unicons
  const dashboardIcon = <UilHome size="24" className="cursor-pointer" />;
  const accountIcon = <UilClipboardAlt size="24" className="cursor-pointer" />;
  const transactionIcon = <UilDollarSign size="24" className="cursor-pointer" />;
  const recurringIcon = <UilFile size="24" className="cursor-pointer" />;
    const reconcileIcon = <UilCheckSquare size="24" className="cursor-pointer" />;
  const budgetIcon = <UilWallet size="24" className="cursor-pointer" />;
  const receiptIcon = <UilReceipt size="24" className="cursor-pointer" />;
  const investmentIcon = <UilChart size="24" className="cursor-pointer" />; // Updated for Investment
  const goalsIcon = <UilBullseye size="24" className="cursor-pointer" />; // Updated for Goals
  const profileIcon = <UilUser size="24" className="cursor-pointer" />;
  const familyIcon = <UilUsersAlt size="24" className="cursor-pointer" />; // Add to Family icon
  const settingsIcon = <UilSetting size="24" className="cursor-pointer" />; // Settings icon
  const cashFlowIcon = <UilTransaction  size="24" className="cursor-pointer" />; // Cash Flow icon
  const notificationIcon = <UilBell size="24" className="cursor-pointer" />;
  const bookKeepingIcon = <UilBookOpen size="24" className="cursor-pointer" />; // Added for BookKeeping
  const stripeIcon = <UilCreditCard size="24" className="cursor-pointer" />; // Stripe icon
  const chatbotIcon = <UilCommentAlt size="24" className="cursor-pointer" />; // Chatbot icon
  const customChartsIcon = <UilDashboard size="24" className="cursor-pointer" />; // Custom Charts icon
 const logoutIcon = <FontAwesomeIcon icon={faSignOutAlt} />;

  const sideMenu = ($darkMode,$module,$moduleId,$linkAlignment,$icon) => {

    return (

      <li>
        <Link
          to="/dashboard/receipts"
          title="Receipts"
          className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'receipts' ? darkMode
            ? 'bg-gray-700'
            : 'bg-[#c5e1a5]'
            : ''
          }`}
          onClick={() => setActivePage('receipts')}
        >
          <span className="mr-2">{receiptIcon}</span>
          {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>Receipts</span>}
        </Link>
    </li>

    );

  }

  // Function for logo image rendering
  const logoImg = (slideIn) => (
    <img
      src={!slideIn ? logo_mini : logo}
      alt="Logo"
      className="p-4 w-full h-full object-cover"
    />
  );
 // pick a real Tailwind width
  // const widthClass = slideIn ? 'w-[70px]' : 'w-[220px]';

  const widthClass = slideIn ? 'w-28' : 'w-56';
  // Conditional class to center content if slideIn is true
  const linkAlignment = slideIn ? 'justify-center' : 'justify-start';


  return (
    <div
    id="sideNav"
    className={`
      ${widthClass}
      fixed top-0 left-0
      h-full
        ${darkMode ? 'bg-gray-800 ' : 'bg-[#8bc34a]'}
        ${darkMode ? 'text-white' : 'text-black'}
      overflow-hidden
      transition-all duration-300 ease-in-out
    `}
    >
      {/* Header */}
      <div
        className={`h-1/9 flex p-2 pr-1 border-b ${darkMode ? 'border-gray-700' : 'border-gray-300'}`}
      >
        <div className="w-3/4 flex">
          <img
            src={slideIn ? logo_mini : logo}
            alt="Logo"
            className={`p-4 w-full h-full object-cover ${darkMode ? 'filter invert' : ''}`}
          />
        </div>
        <div className="w-1/4 flex items-center justify-end pr-1 relative">
          {!slideIn && (
            <div onClick={toggleNotifications} className="cursor-pointer relative mr-1">
              <UilBell size="20" className={darkMode ? 'text-white' : 'text-black'} />
            </div>
          )}
          {!slideIn && (
            <div onClick={togglePennyPalHomepage} className="cursor-pointer relative mr-1">
              <UilWallet size="20" className={darkMode ? 'text-white' : 'text-black'} />
            </div>
          )}
           {!slideIn && (
            <div onClick={toggleLandingpage } className="cursor-pointer relative mr-1">
              <UilBookOpen size="20" className={darkMode ? 'text-white' : 'text-black'} />
            </div>
          )}
          <div
            ref={toggleRef}
            onClick={callback}
            className="cursor-pointer ml-2"
            title="Toggle Menu"
          >
            <UilBars size="24" className={darkMode ? 'text-white' : 'text-black'} />
          </div>
        </div>
      </div>

      {/* Menu Items */}
      <div className="flex flex-col h-[88%]">
        <div
          id="sideNavMenuModules"
          className="flex justify-start w-full border-b border-gray-300"
          style={{ height: '75%' }} // 75% of the main menu area
        >
          <ul className="list-none items-center w-full">
          <div
            className="w-full overflow-y-auto"
            style={{
              maxHeight: '100%',
              scrollbarWidth: 'none',       // Firefox
              msOverflowStyle: 'none',      // IE/Edge
            }}
          >
            {/* Hide scrollbar for Chrome, Safari and Opera */}
            <style>
              {`
              div::-webkit-scrollbar {
                display: none;
              }
            `}
            </style>
            <li>
              <Link
                to="/dashboard"
                title="Dashboard"
                className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'dashboard' ? darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                }`}
                onClick={() => setActivePage('dashboard')}
              >
                <span className="mr-2">{dashboardIcon}</span>
                {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>Dashboard</span>}
              </Link>
            </li>
            {hasPage('Accounts') && (
            <li>
              <Link
                to="/dashboard/accounts"
                title="Accounts"
                className={`flex items-center py-2 px-4 ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'accounts' ? darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                }`}
                onClick={() => setActivePage('accounts')}
              >
                <span className="mr-2">{accountIcon}</span>
                {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>Accounts</span>}
              </Link>
            </li>
            )}
            {hasPage('Transactions') && (
            <li>
              <Link
                to="/dashboard/transactions"
                title="Transactions"
                className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'transactions' ? darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                }`}
                onClick={() => setActivePage('transactions')}
              >
                <span className="mr-2">{transactionIcon}</span>
                {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>Transactions</span>}
              </Link>
            </li>
            )}
            {/* <li>
              <Link
                to="/dashboard/reports"
                className={`flex items-center py-2 px-4 hover:bg-gray-100 ${activePage === 'reports' ? 'active' : ''}`}
                onClick={() => setActivePage('reports')}
              >
                <span className="mr-2">{reportsIcon}</span>
                {!slideIn && <span>Bookkeeping</span>}
              </Link>
            </li> */}
            {hasPage('Reconciliation') && (
            <li>
              <Link
                to="/dashboard/Reconciliation"
                className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'Reconciliation' ?  darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                }`}
                onClick={() => setActivePage('Reconciliation')}
              >
                <span className="mr-2">{reconcileIcon}</span>
                {!slideIn && <span>Reconcile</span>}
              </Link>
            </li>
            )}
            {hasPage('Recurring') && (
            <li>
              <Link
                to="/dashboard/RecurringTransactions"
                title="Recurring"
                className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'RecurringTransactions' ? darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                }`}
                onClick={() => setActivePage('RecurringTransactions')}
              >
                <span className="mr-2">{recurringIcon}</span>
                {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>Recurring</span>}
              </Link>
            </li>
            )}
             {hasPage('Receipts') && (
            <li>
              <Link
                to="/dashboard/receiptitems"
                title="Receipts"
                className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'investment' ? darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                }`}
                onClick={() => setActivePage('receipt-items')} >
              
                <span className="mr-2">{receiptIcon}</span>
                {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>Receipts</span>}
              </Link>
            </li>
            )}
            {hasPage('Budget') && (
            <li>
              <Link
                to="/dashboard/budget"
                title="Budget"
                className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'budget' ? darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                }`}
                onClick={() => setActivePage('budget')}
              >
                <span className="mr-2">{budgetIcon}</span>
                {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>Budget</span>}
              </Link>
            </li>
            )}
            {/* <li>
              <Link
                to="/dashboard/budget-rules"
                className={`flex items-center py-2 px-4 hover:bg-gray-100 ${activePage === 'budget-rules' ? 'active' : ''}`}
                onClick={() => setActivePage('budget-rules')}
              >
                <span className="mr-2">{budgetIcon}</span>
                {!slideIn && <span>Budget Rules</span>}
              </Link>
            </li> */}
            {/* <li>
              <Link
                to="/dashboard/receipts"
                title="Receipts"
                className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'receipts' ? darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                }`}
                onClick={() => setActivePage('receipts')}
              >
                <span className="mr-2">{receiptIcon}</span>
                {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>Receipts</span>}
              </Link>
            </li> */}
            {hasPage('Investment') && (
            <li>
              <Link
                to="/dashboard/investment"
                title="Investment"
                className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'investment' ? darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                }`}
                onClick={() => setActivePage('investment')} >
              
                <span className="mr-2">{investmentIcon}</span>
                {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>Investment</span>}
              </Link>
            </li>
            )}
            {hasPage('Investment') && (
  <li>
    <Link
      to="/dashboard/investmentsdashboard"
      title="Investments Dashboard"
      className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'investmentsdashboard' ? darkMode
        ? 'bg-gray-700'
        : 'bg-[#c5e1a5]'
        : ''
      }`}
      onClick={() => setActivePage('investmentsdashboard')}
    >
      <span className="mr-2">{investmentIcon}</span>
      {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>Investments Dashboard</span>}
    </Link>
  </li>
)}
            {hasPage('Goals') && (
            <li>
              <Link
                to="/dashboard/goals"
                title="Goals"
                className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'goals' ? darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                }`}
                onClick={() => setActivePage('goals')} >
              
                <span className="mr-2">{goalsIcon}</span>
                {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>Goals</span>}
              </Link>
            </li>
            )}
            {/* New Cash Flow Menu Item */ }
            {/*
            <li>
              <Link
                to="/dashboard/investment"
                title="Investment"
                className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'investment' ? darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                }`}
                onClick={() => setActivePage('investment')}
              >
                <span className="mr-2">{faWallet}</span>
                {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>Investment</span>}
              </Link>
            </li>
            <li>
              <Link
                to="dashboard/goals"
                title="Goals"
                className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'goals' ? darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                }`}
                onClick={() => setActivePage('goals')}
              >
                <span className="mr-2">{faBullseye}</span>
                {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>Goals</span>}
              </Link>
            </li>
            */}
            {hasPage('CashFlow') && (
            <li>
              <Link
                to="/dashboard/cashflow"
                title="Cash Flow"
                className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'cashflow' ? darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                }`}
                onClick={() => setActivePage('cashflow')}
              >
                <span className="mr-2">{cashFlowIcon}</span>
                {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>Cash Flow</span>}
              </Link>
            </li>
            )}
            {hasPage('Bookkeeping') && (
            <li>
              <Link
               to="/dashboard/bookkeeping"
                title="bookkeeping"
                className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'bookkeeping' ? darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                  }`}
                onClick={() => setActivePage('bookkeeping')}
              >
                <span className="mr-2">{bookKeepingIcon}</span>
                {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>BookKeeping</span>}
              </Link>
            </li>
            )}
            {/* <li>
              <Link
                to="/dashboard/stripe"
                title="Stripe"
                className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'stripe' ? darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                  }`}
                onClick={() => setActivePage('stripe')}
              >
                <span className="mr-2">{stripeIcon}</span>
                {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>Subscriptions</span>}
              </Link>
            </li> */}
            {hasPage('AI') && (
            <li>
              <Link
                to="/dashboard/chatbot"
                title="Chatbot"
                 className={`flex items-center py-2 px-4  ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'chatbot' ? darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                  }`}
                onClick={() => setActivePage('chatbot')}
              >
                <span className="mr-2">{chatbotIcon}</span>
                {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>AI Assistant</span>}
              </Link>
            </li>
            )}
            {hasPage('AI') && (
            <li>
              <Link
                to="/dashboard/custom-charts"
                title="Custom Charts"
                className={`flex items-center py-2 px-4 ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${
                  activePage === 'custom-charts' ? darkMode
                  ? 'bg-gray-700'
                  : 'bg-[#c5e1a5]'
                  : ''
                  }`}
                onClick={() => setActivePage('custom-charts')}
              >
                <span className="mr-2">{customChartsIcon}</span>
                {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'} `}>AI Dashboard</span>}
              </Link>
            </li>
            )}
          </div>
          </ul>
          
        </div>

        {/* Profile Section (bottom) */}
        <div
          id="sideNavMenuProfile"
          className="flex flex-col items-center w-full justify-center border-t border-gray-300"
          style={{ height: '25%' }} // 25% of the main menu area
        >
          {/* Scrollable Hidden-Scrollbar Wrapper */}
          <div
            className="w-full overflow-y-auto"
            style={{
              maxHeight: '100%',
              scrollbarWidth: 'none',       // Firefox
              msOverflowStyle: 'none',      // IE/Edge
            }}
          >
            {/* Hide scrollbar for Chrome, Safari and Opera */}
            <style>
              {`
              div::-webkit-scrollbar {
                display: none;
              }
            `}
            </style>
       <button
  type="button"
  title="Logout"
    onClick={handleLogout}
  className={`flex items-center w-full py-2 px-4 ${
    darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'
  } ${linkAlignment} ${
    activePage === 'logout'
      ? darkMode
        ? 'bg-gray-700'
        : 'bg-[#c5e1a5]'
      : ''
  }`}
>
  <span className="mr-2">{logoutIcon}</span>
  {!slideIn && (
    <span className={`${darkMode ? 'text-gray-100' : 'text-black'}`}>
      Logout
    </span>
  )}
</button>

          <Link
            to="/dashboard/settings"
            title="Settings"
  className={`flex items-center w-full py-2 px-4 ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'} ${linkAlignment} ${activePage === 'settings' ? (darkMode ? 'bg-gray-700' : 'bg-[#c5e1a5]') : ''}`}
  onClick={() => setActivePage('settings')}
          >
            <span className="mr-2">{settingsIcon}</span>
  {!slideIn && <span className={`${darkMode ? 'text-gray-100' : 'text-black'}`}>Settings</span>}
          </Link>

            <div
              className={`flex items-center w-full py-2 px-4 ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#c4ff80]'
                } cursor-pointer ${slideIn ? 'justify-center' : 'justify-start'}`}
              onClick={toggleDarkMode}
            >
              <span className="mr-2">
                {darkMode ? (
                  <UilSun size="24" className="text-yellow-400" />
                ) : (
                  <UilMoon size="24" className="text-black" />
                )}
              </span>
              {!slideIn && <span>{darkMode ? 'Light' : 'Dark'}</span>}
            </div>
          
          </div>
        </div>
      </div>
    

    </div>
  );
};

  export default SideNav;
